import os
import json

# Create project structure for AccureMD Teams Extension
project_structure = {
    "AccureMD.TeamsBot": {
        "Controllers": [
            "BotController.cs",
            "AuthController.cs",
            "MeetingController.cs"
        ],
        "Services": [
            "AccureMDBotService.cs",
            "AuthenticationService.cs",
            "MeetingService.cs",
            "RecordingService.cs",
            "TranscriptionService.cs",
            "StorageService.cs"
        ],
        "Models": [
            "MeetingModel.cs",
            "TranscriptModel.cs",
            "AuthenticationModel.cs"
        ],
        "wwwroot": {
            "css": ["teams-app.css"],
            "js": ["teams-app.js"],
            "html": ["configure.html", "index.html"]
        },
        "TeamsAppManifest": [
            "manifest.json",
            "color.png",
            "outline.png"
        ],
        "Config": [
            "appsettings.json",
            "appsettings.Development.json"
        ],
        "Root": [
            "Program.cs",
            "Startup.cs",
            "AccureMD.TeamsBot.csproj"
        ]
    }
}

print("AccureMD Teams Extension Project Structure:")
print("=" * 50)

def print_structure(structure, indent=0):
    for key, value in structure.items():
        print("  " * indent + f"📁 {key}/")
        if isinstance(value, dict):
            print_structure(value, indent + 1)
        elif isinstance(value, list):
            for item in value:
                print("  " * (indent + 1) + f"📄 {item}")

print_structure(project_structure)