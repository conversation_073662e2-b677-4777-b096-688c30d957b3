<!DOCTYPE html>
<html>
<head>
    <title>AccureMD Configuration</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <link rel="stylesheet" href="/css/teams-app.css">
</head>
<body class="configure-page">
    <div class="container">
        <div class="header">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzYyNjRBNyIvPgo8cGF0aCBkPSJNOCAxMkg24CIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTggMThIMjQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik04IDI0SDI0IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="AccureMD" class="logo">
            <h1>AccureMD Configuration</h1>
            <p>Configure your AI meeting assistant</p>
        </div>

        <div class="config-form">
            <div class="form-group">
                <label for="botName">Bot Display Name:</label>
                <input type="text" id="botName" value="AccureMD Assistant" />
            </div>

            <div class="form-group">
                <label for="autoRecord">Auto-start recording:</label>
                <input type="checkbox" id="autoRecord" />
            </div>

            <div class="form-group">
                <label for="autoTranscribe">Auto-start transcription:</label>
                <input type="checkbox" id="autoTranscribe" checked />
            </div>

            <div class="form-group">
                <label for="language">Transcription Language:</label>
                <select id="language">
                    <option value="en-US">English (US)</option>
                    <option value="en-GB">English (UK)</option>
                    <option value="es-ES">Spanish</option>
                    <option value="fr-FR">French</option>
                    <option value="de-DE">German</option>
                </select>
            </div>
        </div>

        <div class="actions">
            <button id="saveBtn" class="primary-btn">Save Configuration</button>
            <button id="cancelBtn" class="secondary-btn">Cancel</button>
        </div>
    </div>

    <script>
        microsoftTeams.app.initialize().then(() => {
            microsoftTeams.pages.config.registerOnSaveHandler((saveEvent) => {
                const config = {
                    botName: document.getElementById('botName').value,
                    autoRecord: document.getElementById('autoRecord').checked,
                    autoTranscribe: document.getElementById('autoTranscribe').checked,
                    language: document.getElementById('language').value
                };

                microsoftTeams.pages.config.setConfig({
                    suggestedDisplayName: 'AccureMD',
                    contentUrl: window.location.origin + '/index.html',
                    websiteUrl: window.location.origin + '/index.html',
                    entityId: 'accureMD-config',
                    removeUrl: window.location.origin + '/remove.html'
                }).then((result) => {
                    saveEvent.notifySuccess();
                }).catch((error) => {
                    console.error('Error saving config:', error);
                    saveEvent.notifyFailure('Failed to save configuration');
                });
            });

            microsoftTeams.pages.config.setValidityState(true);

            document.getElementById('saveBtn').addEventListener('click', () => {
                microsoftTeams.pages.config.registerOnSaveHandler();
            });

            document.getElementById('cancelBtn').addEventListener('click', () => {
                microsoftTeams.pages.config.setValidityState(false);
            });
        });
    </script>
</body>
</html>