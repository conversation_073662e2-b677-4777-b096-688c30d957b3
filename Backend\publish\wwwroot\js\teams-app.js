// AccureMD Teams App JavaScript

class TeamsApp {
    constructor() {
        console.log('AccureMD: TeamsApp constructor called');

        this.currentUser = null;
        this.currentMeeting = null;
        this.isRecording = false;
        this.isTranscribing = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.transcripts = [];
        this.teamsContext = null;

        console.log('AccureMD: Starting initialization...');
        this.initializeTeamsContext();
        this.setupEventListeners();
        console.log('AccureMD: Constructor completed');
    }

    async initializeTeamsContext() {
        console.log('AccureMD: Starting app initialization...');

        // Set a timeout to ensure something shows up
        setTimeout(() => {
            if (document.getElementById('loadingScreen').style.display !== 'none') {
                console.log('AccureMD: Initialization timeout, showing auth screen');
                this.hideLoading();
                this.showAuthScreen();
            }
        }, 3000);

        try {
            // Check if we're in Teams context
            if (typeof microsoftTeams === 'undefined') {
                console.log('AccureMD: Teams SDK not available, running in standalone mode');
                this.hideLoading();
                await this.checkAuthenticationStatus();
                return;
            }

            // Initialize Teams SDK with timeout
            try {
                console.log('AccureMD: Initializing Teams SDK...');
                const initPromise = microsoftTeams.app.initialize();
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Teams initialization timeout')), 5000)
                );

                await Promise.race([initPromise, timeoutPromise]);
                console.log('AccureMD: Teams SDK initialized successfully');

                // Try to get context
                try {
                    const context = await microsoftTeams.app.getContext();
                    console.log('AccureMD: Teams context obtained:', context);
                    this.teamsContext = context;
                } catch (contextError) {
                    console.warn('AccureMD: Could not get Teams context:', contextError);
                }

            } catch (initError) {
                console.warn('AccureMD: Teams SDK initialization failed, continuing anyway:', initError);
            }

            // Always hide loading and proceed
            this.hideLoading();
            await this.checkAuthenticationStatus();

        } catch (error) {
            console.error('AccureMD: App initialization error:', error);
            this.hideLoading();
            this.showAuthScreen();
        }
    }

    setupEventListeners() {
        // Authentication
        document.getElementById('loginBtn')?.addEventListener('click', () => this.login());
        document.getElementById('logoutBtn')?.addEventListener('click', () => this.logout());

        // Meeting controls
        document.getElementById('joinBtn')?.addEventListener('click', () => this.joinMeeting());

        // Recording controls
        document.getElementById('startRecordingBtn')?.addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecordingBtn')?.addEventListener('click', () => this.stopRecording());
        document.getElementById('startTranscriptionBtn')?.addEventListener('click', () => this.startTranscription());

        // Transcript controls
        document.getElementById('clearTranscriptBtn')?.addEventListener('click', () => this.clearTranscript());
        document.getElementById('downloadTranscriptBtn')?.addEventListener('click', () => this.downloadTranscript());

        // Real-time updates
        this.startLiveUpdates();
    }

    async checkAuthenticationStatus() {
        try {
            let userId = 'anonymous';

            // Try to get user ID from Teams context
            try {
                if (typeof microsoftTeams !== 'undefined') {
                    const context = await microsoftTeams.app.getContext();
                    userId = context.user?.id || 'anonymous';
                    console.log('AccureMD: Got Teams user ID:', userId);
                }
            } catch (contextError) {
                console.warn('AccureMD: Could not get Teams context for auth check:', contextError);
            }

            console.log('AccureMD: Checking auth status for user:', userId);
            const response = await fetch(`/api/auth/status/${userId}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const authStatus = await response.json();
            console.log('AccureMD: Auth status response:', authStatus);

            if (authStatus.isAuthenticated) {
                console.log('AccureMD: User is authenticated, showing main app');
                this.currentUser = authStatus;
                this.showMainApp();
                this.updateUserInterface();
            } else {
                console.log('AccureMD: User is not authenticated, showing auth screen');
                this.showAuthScreen();
            }
        } catch (error) {
            console.error('AccureMD: Error checking authentication:', error);
            this.showAuthScreen();
        }
    }

    async login() {
        console.log('AccureMD: Starting login process...');

        try {
            // Check if we're in Teams context
            if (typeof microsoftTeams !== 'undefined' && this.isInTeams) {
                console.log('AccureMD: Using Teams authentication flow');

                // Use Teams authentication API with our auth start page
                const authStartUrl = window.location.origin + '/html/auth-start.html';
                console.log('AccureMD: Opening authentication popup with URL:', authStartUrl);

                const result = await microsoftTeams.authentication.authenticate({
                    url: authStartUrl,
                    width: 600,
                    height: 535
                });

                console.log('AccureMD: Authentication result:', result);

                // The result should be a localStorage key containing the auth data
                let authResult;
                try {
                    if (typeof result === 'string' && result.startsWith('accuremd.auth.result.')) {
                        // Get the stored result from localStorage
                        const storedData = localStorage.getItem(result);
                        if (storedData) {
                            authResult = JSON.parse(storedData);
                            // Clean up the stored data
                            localStorage.removeItem(result);
                        } else {
                            throw new Error('Authentication data not found in storage');
                        }
                    } else {
                        // Fallback: try to parse as direct JSON
                        authResult = typeof result === 'string' ? JSON.parse(result) : result;
                    }
                } catch (e) {
                    console.error('AccureMD: Failed to parse auth result:', e);
                    throw new Error('Invalid authentication response');
                }

                if (authResult && authResult.accessToken) {
                    console.log('AccureMD: Authentication successful');
                    this.currentUser = authResult;
                    this.showMainApp();
                    this.showNotification('Successfully signed in!', 'success');
                } else {
                    throw new Error('No access token received');
                }

            } else {
                console.log('AccureMD: Using fallback popup authentication');

                // Fallback for non-Teams environments
                let userId = 'anonymous';
                if (this.teamsContext && this.teamsContext.user) {
                    userId = this.teamsContext.user.id || 'anonymous';
                }

                const redirectUri = window.location.origin + '/html/auth-callback.html';
                const authUrl = `/api/auth/login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;

                const response = await fetch(authUrl);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const authData = await response.json();
                if (authData.success && authData.authUrl) {
                    this.openAuthPopup(authData.authUrl);
                } else {
                    throw new Error(authData.message || 'Failed to get authentication URL');
                }
            }

        } catch (error) {
            console.error('AccureMD: Login failed:', error);
            this.showError(`Authentication failed: ${error.message}`);
        }
    }

    openAuthPopup(authUrl) {
        console.log('Opening popup for URL:', authUrl);

        try {
            const popup = window.open(authUrl, 'auth', 'width=600,height=600,scrollbars=yes,resizable=yes,location=yes');

            if (!popup) {
                throw new Error('Popup blocked by browser');
            }

            // Poll for popup closure or message
            const checkClosed = setInterval(() => {
                try {
                    if (popup.closed) {
                        clearInterval(checkClosed);
                        console.log('Popup closed, checking authentication status...');
                        // Check authentication status after popup closes
                        setTimeout(() => {
                            this.checkAuthenticationStatus();
                        }, 1000);
                    }
                } catch (error) {
                    // Popup might be on different domain, ignore cross-origin errors
                    console.log('Popup check error (normal for cross-origin):', error.message);
                }
            }, 1000);

            // Also listen for messages from the popup
            const messageHandler = (event) => {
                if (event.origin === window.location.origin) {
                    console.log('Received message from popup:', event.data);
                    if (event.data.type === 'auth-success') {
                        clearInterval(checkClosed);
                        window.removeEventListener('message', messageHandler);
                        popup.close();
                        this.checkAuthenticationStatus();
                    } else if (event.data.type === 'auth-error') {
                        clearInterval(checkClosed);
                        window.removeEventListener('message', messageHandler);
                        popup.close();
                        this.showError('Authentication failed: ' + event.data.error);
                    }
                }
            };

            window.addEventListener('message', messageHandler);

        } catch (error) {
            console.error('Failed to open popup:', error);

            // Fallback: redirect to auth URL directly
            console.log('Popup failed, trying direct redirect...');
            const userConfirmed = confirm('Popup blocked. Redirect to login page? (You will need to navigate back manually)');
            if (userConfirmed) {
                window.location.href = authUrl;
            } else {
                this.showError('Authentication cancelled. Please allow popups or try again.');
            }
        }
    }

    async logout() {
        try {
            const userId = this.currentUser?.userId || 'anonymous';
            await fetch(`/api/auth/logout/${userId}`, { method: 'POST' });

            this.currentUser = null;
            this.currentMeeting = null;
            this.showAuthScreen();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    async joinMeeting() {
        try {
            const meetingUrl = document.getElementById('meetingUrl').value.trim();
            if (!meetingUrl) {
                this.showError('Please enter a valid Teams meeting URL');
                return;
            }

            this.updateConnectionStatus('Connecting...', 'connecting');

            const response = await fetch('/api/meetings/join', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    meetingUrl: meetingUrl,
                    userId: this.currentUser.userId,
                    displayName: 'AccureMD Bot'
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentMeeting = {
                    id: result.meetingId,
                    url: meetingUrl,
                    joinedAt: new Date()
                };

                this.updateConnectionStatus('Connected to meeting', 'online');
                this.showRecordingControls();
                this.showSuccess('Successfully joined meeting as AccureMD Bot!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Join meeting error:', error);
            this.updateConnectionStatus('Connection failed', 'offline');
            this.showError('Failed to join meeting: ' + error.message);
        }
    }

    async startRecording() {
        try {
            if (!this.currentMeeting) {
                this.showError('Please join a meeting first');
                return;
            }

            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.isRecording = true;
                this.recordingStartTime = new Date();
                this.startRecordingTimer();
                this.updateRecordingInterface();
                this.showSuccess('Recording started!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Start recording error:', error);
            this.showError('Failed to start recording: ' + error.message);
        }
    }

    async stopRecording() {
        try {
            if (!this.currentMeeting || !this.isRecording) {
                return;
            }

            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/stop`, {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.isRecording = false;
                this.stopRecordingTimer();
                this.updateRecordingInterface();
                this.showSuccess('Recording stopped and saved!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Stop recording error:', error);
            this.showError('Failed to stop recording: ' + error.message);
        }
    }

    async startTranscription() {
        try {
            if (!this.currentMeeting) {
                this.showError('Please join a meeting first');
                return;
            }

            this.isTranscribing = true;
            this.updateTranscriptionInterface();
            this.showSuccess('Live transcription started!');

            // Start fetching transcripts
            this.startTranscriptUpdates();
        } catch (error) {
            console.error('Start transcription error:', error);
            this.showError('Failed to start transcription: ' + error.message);
        }
    }

    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = new Date() - this.recordingStartTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);

                const timerText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('recordingTimer').textContent = timerText;
            }
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    async startTranscriptUpdates() {
        if (!this.currentMeeting || !this.isTranscribing) return;

        try {
            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/transcripts`);
            const transcripts = await response.json();

            // Add new transcripts
            transcripts.forEach(transcript => {
                if (!this.transcripts.find(t => t.id === transcript.id)) {
                    this.transcripts.push(transcript);
                    this.addTranscriptToUI(transcript);
                }
            });

            // Continue polling for updates
            setTimeout(() => this.startTranscriptUpdates(), 3000);
        } catch (error) {
            console.error('Error fetching transcripts:', error);
        }
    }

    addTranscriptToUI(transcript) {
        const container = document.getElementById('transcriptContainer');

        // Remove "no transcript" message if it exists
        const noTranscript = container.querySelector('.no-transcript');
        if (noTranscript) {
            noTranscript.remove();
        }

        const transcriptElement = document.createElement('div');
        transcriptElement.className = 'transcript-entry';
        transcriptElement.innerHTML = `
            <div class="transcript-header">
                <span class="speaker-name">${transcript.speakerName}</span>
                <span class="timestamp">${new Date(transcript.timestamp).toLocaleTimeString()}</span>
            </div>
            <div class="transcript-text">${transcript.text}</div>
            <div class="confidence-score">Confidence: ${Math.round(transcript.confidence * 100)}%</div>
        `;

        container.appendChild(transcriptElement);
        container.scrollTop = container.scrollHeight;
    }

    clearTranscript() {
        this.transcripts = [];
        const container = document.getElementById('transcriptContainer');
        container.innerHTML = '<div class="no-transcript">Transcript cleared. Start transcription to see new captions...</div>';
    }

    downloadTranscript() {
        if (this.transcripts.length === 0) {
            this.showError('No transcript available to download');
            return;
        }

        const transcriptData = {
            meeting: this.currentMeeting,
            transcripts: this.transcripts,
            generatedAt: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(transcriptData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `AccureMD_Transcript_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    startLiveUpdates() {
        // Poll for bot status updates
        setInterval(async () => {
            try {
                const response = await fetch('/api/meetings/status');
                const data = await response.json();

                // Update status display if needed
                console.log('Bot status:', data.status);
            } catch (error) {
                console.error('Error getting bot status:', error);
            }
        }, 10000); // Every 10 seconds
    }

    // UI Helper Methods
    hideLoading() {
        document.getElementById('loadingScreen').style.display = 'none';
    }

    showAuthScreen() {
        document.getElementById('authScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
    }

    showMainApp() {
        document.getElementById('authScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'flex';
    }

    updateUserInterface() {
        if (this.currentUser) {
            document.getElementById('userName').textContent = this.currentUser.userName || 'User';
            document.getElementById('userAvatar').textContent = (this.currentUser.userName || 'U').charAt(0).toUpperCase();
        }
    }

    showRecordingControls() {
        document.getElementById('recordingControls').style.display = 'block';
    }

    updateConnectionStatus(text, status) {
        const statusElement = document.getElementById('connectionStatus');
        const dot = statusElement.querySelector('.status-dot');
        const textElement = statusElement.querySelector('span:last-child');

        dot.className = `status-dot ${status}`;
        textElement.textContent = text;
    }

    updateRecordingInterface() {
        const startBtn = document.getElementById('startRecordingBtn');
        const stopBtn = document.getElementById('stopRecordingBtn');
        const indicator = document.getElementById('recordingIndicator');

        if (this.isRecording) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-block';
            indicator.textContent = '🔴 Recording';
            indicator.style.color = 'var(--recording-color)';
        } else {
            startBtn.style.display = 'inline-block';
            stopBtn.style.display = 'none';
            indicator.textContent = '⚪ Standby';
            indicator.style.color = 'var(--teams-text-secondary)';
        }
    }

    updateTranscriptionInterface() {
        const btn = document.getElementById('startTranscriptionBtn');
        if (this.isTranscribing) {
            btn.textContent = 'Transcribing...';
            btn.disabled = true;
            btn.style.backgroundColor = 'var(--success-color)';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 4px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            max-width: 300px;
            background-color: ${type === 'success' ? 'var(--success-color)' : 'var(--error-color)'};
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
}

// TeamsApp class is ready for initialization
// Initialization is handled by index.html to avoid conflicts