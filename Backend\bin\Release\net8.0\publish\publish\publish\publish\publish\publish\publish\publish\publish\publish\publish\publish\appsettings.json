{"MicrosoftAppType": "SingleTenant", "MicrosoftAppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "MicrosoftAppPassword": "****************************************", "MicrosoftAppTenantId": "653f2dfa-0545-4c9f-be75-7c35da274877", "BaseUrl": "https://accuremd.azurewebsites.net", "ConnectionStrings": {"DefaultConnection": "", "StorageAccount": ""}, "TranscriptionService": {"ApiUrl": "", "ApiKey": "", "SpeechRegion": "", "SpeechKey": ""}, "Recording": {"StoragePath": "./recordings", "MaxRecordingDurationMinutes": 240, "SupportedFormats": ["mp4", "wav", "mp3"]}, "Teams": {"AppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "AppSecret": "****************************************", "TenantId": "653f2dfa-0545-4c9f-be75-7c35da274877"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Bot": "Information"}}, "AllowedHosts": "*"}