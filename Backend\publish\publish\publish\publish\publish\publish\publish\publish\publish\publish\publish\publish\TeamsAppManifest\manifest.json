{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "packageName": "com.accuremd.teamsbot", "developer": {"name": "AccureMD", "websiteUrl": "https://accuremd.azurewebsites.net", "privacyUrl": "https://accuremd.azurewebsites.net/html/privacy.html", "termsOfUseUrl": "https://accuremd.azurewebsites.net/html/termsofuse.html"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "AccureMD", "full": "AccureMD AI Meeting Assistant"}, "description": {"short": "AI assistant for meeting recording and transcription.", "full": "<PERSON><PERSON><PERSON><PERSON> is an AI-powered meeting assistant that joins Teams meetings to provide high-quality recording and live transcription, similar to Read.AI."}, "accentColor": "#6264A7", "bots": [{"botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "scopes": ["personal", "team", "groupchat"], "supportsFiles": false, "isNotificationOnly": false, "supportsCalling": true, "supportsVideo": true}], "configurableTabs": [{"configurationUrl": "https://accuremd.azurewebsites.net/html/configure.html", "canUpdateConfiguration": true, "scopes": ["team", "groupchat"]}], "staticTabs": [{"entityId": "accuremd.personal.tab", "name": "AccureMD Assistant", "contentUrl": "https://accuremd.azurewebsites.net/html/index.html", "websiteUrl": "https://accuremd.azurewebsites.net/html/index.html", "scopes": ["personal"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["accuremd.azurewebsites.net", "*.ngrok.io"], "webApplicationInfo": {"id": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "resource": "api://accuremd.azurewebsites.net/24a397f4-16dd-4dae-8b8f-5368c3a81fed"}}