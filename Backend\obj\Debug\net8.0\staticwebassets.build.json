{"Version": 1, "Hash": "Qq/5C4wNlNmnKPwexi8OPTeqqWQ0J6PAiB/E9M/z0zQ=", "Source": "AccureMD.TeamsBot", "BasePath": "_content/AccureMD.TeamsBot", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AccureMD.TeamsBot\\wwwroot", "Source": "AccureMD.TeamsBot", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "Pattern": "**"}], "Assets": [{"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1i87atu4yk", "Integrity": "yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 9483, "LastWriteTime": "2025-08-07T21:24:30+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4m0ksaowe9", "Integrity": "HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 7160, "LastWriteTime": "2025-08-08T16:43:38+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0k2wjtqfvf", "Integrity": "EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 4697, "LastWriteTime": "2025-08-08T16:42:10+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "77xizkbh7e", "Integrity": "bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4122, "LastWriteTime": "2025-08-08T17:03:50+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9rv7a2k3wu", "Integrity": "hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6452, "LastWriteTime": "2025-08-08T10:49:00+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\remove.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/remove#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "klzr35bbdh", "Integrity": "CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\remove.html", "FileLength": 2580, "LastWriteTime": "2025-08-08T17:04:10+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gpjzragfzl", "Integrity": "fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 23815, "LastWriteTime": "2025-08-08T16:44:38+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5n4anau1vc", "Integrity": "ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 5970, "LastWriteTime": "2025-08-08T16:46:34+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16+00:00"}], "Endpoints": [{"Route": "css/teams-app.1i87atu4yk.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1i87atu4yk"}, {"Name": "label", "Value": "css/teams-app.css"}, {"Name": "integrity", "Value": "sha256-yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI="}]}, {"Route": "css/teams-app.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI="}]}, {"Route": "html/auth-callback.4m0ksaowe9.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:43:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4m0ksaowe9"}, {"Name": "label", "Value": "html/auth-callback.html"}, {"Name": "integrity", "Value": "sha256-HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk="}]}, {"Route": "html/auth-callback.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:43:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk="}]}, {"Route": "html/auth-start.0k2wjtqfvf.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4697"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:42:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0k2wjtqfvf"}, {"Name": "label", "Value": "html/auth-start.html"}, {"Name": "integrity", "Value": "sha256-EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk="}]}, {"Route": "html/auth-start.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4697"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:42:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk="}]}, {"Route": "html/configure.77xizkbh7e.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4122"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:03:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "77xizkbh7e"}, {"Name": "label", "Value": "html/configure.html"}, {"Name": "integrity", "Value": "sha256-bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk="}]}, {"Route": "html/configure.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4122"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:03:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk="}]}, {"Route": "html/index.9rv7a2k3wu.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 10:49:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9rv7a2k3wu"}, {"Name": "label", "Value": "html/index.html"}, {"Name": "integrity", "Value": "sha256-hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ="}]}, {"Route": "html/index.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 10:49:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "label", "Value": "html/privacy.html"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/privacy.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/remove.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\remove.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2580"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:04:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8="}]}, {"Route": "html/remove.klzr35bbdh.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\remove.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2580"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:04:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "klzr35bbdh"}, {"Name": "label", "Value": "html/remove.html"}, {"Name": "integrity", "Value": "sha256-CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8="}]}, {"Route": "html/termsofuse.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "label", "Value": "html/termsofuse.html"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "js/teams-app.gpjzragfzl.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23815"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:44:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gpjzragfzl"}, {"Name": "label", "Value": "js/teams-app.js"}, {"Name": "integrity", "Value": "sha256-fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE="}]}, {"Route": "js/teams-app.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23815"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:44:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE="}]}, {"Route": "teams-test.5n4anau1vc.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5970"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:46:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5n4anau1vc"}, {"Name": "label", "Value": "teams-test.html"}, {"Name": "integrity", "Value": "sha256-ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE="}]}, {"Route": "teams-test.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5970"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 16:46:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "label", "Value": "test-auth.html"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-auth.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}]}