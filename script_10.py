# Create CSS and JavaScript files for the Teams app
teams_css = '''/* AccureMD Teams App Styles */

:root {
    --teams-primary: #6264A7;
    --teams-secondary: #464775;
    --teams-accent: #A4A3D1;
    --teams-background: #F8F8F8;
    --teams-surface: #FFFFFF;
    --teams-text: #252423;
    --teams-text-secondary: #605E5C;
    --success-color: #107C10;
    --warning-color: #FF8C00;
    --error-color: #D13438;
    --recording-color: #C50E20;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--teams-background);
    color: var(--teams-text);
    line-height: 1.5;
}

.loading-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.loader {
    width: 40px;
    height: 40px;
    border: 4px solid var(--teams-accent);
    border-top: 4px solid var(--teams-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.auth-container {
    text-align: center;
    padding: 32px;
    max-width: 400px;
}

.logo-large {
    width: 64px;
    height: 64px;
    margin-bottom: 24px;
}

.auth-container h1 {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--teams-primary);
}

.auth-container p {
    color: var(--teams-text-secondary);
    margin-bottom: 32px;
    font-size: 14px;
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    width: 100%;
}

.login-btn:hover {
    background-color: var(--teams-secondary);
}

.ms-icon {
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSI+PHBhdGggZD0iTTAgMGgxMHYxMEgweiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0xMSAwaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0wIDExaDEwdjEwSDB6IiBmaWxsPSIjZjlmOWY5Ii8+PHBhdGggZD0iTTExIDExaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjwvc3ZnPg==') no-repeat center;
    margin-right: 8px;
}

.main-app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--teams-surface);
    border-bottom: 1px solid #E1DFDD;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--teams-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.logout-btn {
    background: none;
    border: 1px solid var(--teams-primary);
    color: var(--teams-primary);
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.meeting-controls, .recording-controls, .transcript-panel {
    background-color: var(--teams-surface);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.meeting-controls h2, .recording-controls h2, .transcript-panel h2 {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--teams-primary);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    font-size: 14px;
    color: var(--teams-text);
}

.input-group input[type="url"] {
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
}

.input-group input[type="url"]:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.primary-btn, .record-btn, .stop-btn, .transcribe-btn {
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    margin-top: 8px;
}

.primary-btn:hover {
    background-color: var(--teams-secondary);
}

.record-btn {
    background-color: var(--recording-color);
}

.record-btn:hover {
    background-color: #A01017;
}

.stop-btn {
    background-color: var(--teams-text-secondary);
}

.stop-btn:hover {
    background-color: #323130;
}

.transcribe-btn {
    background-color: var(--success-color);
}

.transcribe-btn:hover {
    background-color: #0B5A0B;
}

.secondary-btn {
    background-color: transparent;
    color: var(--teams-primary);
    border: 1px solid var(--teams-primary);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.status-display {
    margin-top: 16px;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.offline {
    background-color: var(--teams-text-secondary);
}

.status-dot.online {
    background-color: var(--success-color);
}

.status-dot.recording {
    background-color: var(--recording-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.control-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.recording-status {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.timer {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    color: var(--teams-text);
}

.recording-indicator {
    font-size: 14px;
    font-weight: 600;
}

.transcript-container {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #E1DFDD;
    border-radius: 4px;
    padding: 16px;
    background-color: var(--teams-background);
}

.transcript-entry {
    margin-bottom: 12px;
    padding: 8px;
    background-color: var(--teams-surface);
    border-radius: 4px;
    border-left: 3px solid var(--teams-primary);
}

.transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: var(--teams-text-secondary);
}

.speaker-name {
    font-weight: 600;
    color: var(--teams-primary);
}

.timestamp {
    font-family: 'Courier New', monospace;
}

.transcript-text {
    font-size: 14px;
    line-height: 1.4;
}

.confidence-score {
    font-size: 11px;
    color: var(--teams-text-secondary);
    margin-top: 4px;
}

.no-transcript {
    text-align: center;
    color: var(--teams-text-secondary);
    font-style: italic;
    padding: 40px 20px;
}

.transcript-controls {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.footer {
    background-color: var(--teams-surface);
    border-top: 1px solid #E1DFDD;
    padding: 12px 20px;
    text-align: center;
    color: var(--teams-text-secondary);
    font-size: 12px;
}

/* Configure page styles */
.configure-page {
    background-color: var(--teams-surface);
    padding: 20px;
}

.configure-page .container {
    max-width: 600px;
    margin: 0 auto;
}

.configure-page .header {
    text-align: center;
    margin-bottom: 32px;
    border-bottom: none;
    padding-bottom: 0;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 16px;
}

.configure-page h1 {
    font-size: 24px;
    color: var(--teams-primary);
    margin-bottom: 8px;
}

.configure-page p {
    color: var(--teams-text-secondary);
}

.config-form {
    background-color: var(--teams-background);
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--teams-text);
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.actions {
    text-align: center;
}

.actions button {
    margin: 0 8px;
    min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 16px;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .recording-status {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .transcript-controls {
        flex-direction: column;
    }
}'''

teams_js = '''// AccureMD Teams App JavaScript

class AccureMDApp {
    constructor() {
        this.currentUser = null;
        this.currentMeeting = null;
        this.isRecording = false;
        this.isTranscribing = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.transcripts = [];
        
        this.initializeTeamsContext();
        this.setupEventListeners();
    }

    async initializeTeamsContext() {
        try {
            await microsoftTeams.app.initialize();
            console.log('Teams context initialized');
            
            // Get Teams context
            const context = await microsoftTeams.app.getContext();
            console.log('Teams context:', context);
            
            this.hideLoading();
            
            // Check authentication status
            await this.checkAuthenticationStatus();
            
        } catch (error) {
            console.error('Failed to initialize Teams context:', error);
            this.showError('Failed to initialize Teams app');
        }
    }

    setupEventListeners() {
        // Authentication
        document.getElementById('loginBtn')?.addEventListener('click', () => this.login());
        document.getElementById('logoutBtn')?.addEventListener('click', () => this.logout());
        
        // Meeting controls
        document.getElementById('joinBtn')?.addEventListener('click', () => this.joinMeeting());
        
        // Recording controls
        document.getElementById('startRecordingBtn')?.addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecordingBtn')?.addEventListener('click', () => this.stopRecording());
        document.getElementById('startTranscriptionBtn')?.addEventListener('click', () => this.startTranscription());
        
        // Transcript controls
        document.getElementById('clearTranscriptBtn')?.addEventListener('click', () => this.clearTranscript());
        document.getElementById('downloadTranscriptBtn')?.addEventListener('click', () => this.downloadTranscript());
        
        // Real-time updates
        this.startLiveUpdates();
    }

    async checkAuthenticationStatus() {
        try {
            // Get user ID from Teams context
            const context = await microsoftTeams.app.getContext();
            const userId = context.user?.id || 'anonymous';
            
            const response = await fetch(`/api/auth/status/${userId}`);
            const authStatus = await response.json();
            
            if (authStatus.isAuthenticated) {
                this.currentUser = authStatus;
                this.showMainApp();
                this.updateUserInterface();
            } else {
                this.showAuthScreen();
            }
        } catch (error) {
            console.error('Error checking authentication:', error);
            this.showAuthScreen();
        }
    }

    async login() {
        try {
            const context = await microsoftTeams.app.getContext();
            const userId = context.user?.id || 'anonymous';
            const redirectUri = window.location.origin + '/auth-callback';
            
            // Initiate OAuth flow
            const response = await fetch(`/api/auth/login?userId=${userId}&redirectUri=${encodeURIComponent(redirectUri)}`);
            const authData = await response.json();
            
            if (authData.success) {
                // Open authentication window
                microsoftTeams.authentication.authenticate({
                    url: authData.authUrl,
                    width: 600,
                    height: 535,
                    successCallback: (result) => {
                        console.log('Authentication successful:', result);
                        this.checkAuthenticationStatus();
                    },
                    failureCallback: (reason) => {
                        console.error('Authentication failed:', reason);
                        this.showError('Authentication failed. Please try again.');
                    }
                });
            } else {
                throw new Error(authData.message);
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Failed to initiate login. Please try again.');
        }
    }

    async logout() {
        try {
            const userId = this.currentUser?.userId || 'anonymous';
            await fetch(`/api/auth/logout/${userId}`, { method: 'POST' });
            
            this.currentUser = null;
            this.currentMeeting = null;
            this.showAuthScreen();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    async joinMeeting() {
        try {
            const meetingUrl = document.getElementById('meetingUrl').value.trim();
            if (!meetingUrl) {
                this.showError('Please enter a valid Teams meeting URL');
                return;
            }

            this.updateConnectionStatus('Connecting...', 'connecting');
            
            const response = await fetch('/api/meetings/join', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    meetingUrl: meetingUrl,
                    userId: this.currentUser.userId,
                    displayName: 'AccureMD Bot'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentMeeting = {
                    id: result.meetingId,
                    url: meetingUrl,
                    joinedAt: new Date()
                };
                
                this.updateConnectionStatus('Connected to meeting', 'online');
                this.showRecordingControls();
                this.showSuccess('Successfully joined meeting as AccureMD Bot!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Join meeting error:', error);
            this.updateConnectionStatus('Connection failed', 'offline');
            this.showError('Failed to join meeting: ' + error.message);
        }
    }

    async startRecording() {
        try {
            if (!this.currentMeeting) {
                this.showError('Please join a meeting first');
                return;
            }

            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = true;
                this.recordingStartTime = new Date();
                this.startRecordingTimer();
                this.updateRecordingInterface();
                this.showSuccess('Recording started!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Start recording error:', error);
            this.showError('Failed to start recording: ' + error.message);
        }
    }

    async stopRecording() {
        try {
            if (!this.currentMeeting || !this.isRecording) {
                return;
            }

            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/stop`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.isRecording = false;
                this.stopRecordingTimer();
                this.updateRecordingInterface();
                this.showSuccess('Recording stopped and saved!');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Stop recording error:', error);
            this.showError('Failed to stop recording: ' + error.message);
        }
    }

    async startTranscription() {
        try {
            if (!this.currentMeeting) {
                this.showError('Please join a meeting first');
                return;
            }

            this.isTranscribing = true;
            this.updateTranscriptionInterface();
            this.showSuccess('Live transcription started!');
            
            // Start fetching transcripts
            this.startTranscriptUpdates();
        } catch (error) {
            console.error('Start transcription error:', error);
            this.showError('Failed to start transcription: ' + error.message);
        }
    }

    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = new Date() - this.recordingStartTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                const timerText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('recordingTimer').textContent = timerText;
            }
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    async startTranscriptUpdates() {
        if (!this.currentMeeting || !this.isTranscribing) return;
        
        try {
            const response = await fetch(`/api/meetings/${this.currentMeeting.id}/transcripts`);
            const transcripts = await response.json();
            
            // Add new transcripts
            transcripts.forEach(transcript => {
                if (!this.transcripts.find(t => t.id === transcript.id)) {
                    this.transcripts.push(transcript);
                    this.addTranscriptToUI(transcript);
                }
            });
            
            // Continue polling for updates
            setTimeout(() => this.startTranscriptUpdates(), 3000);
        } catch (error) {
            console.error('Error fetching transcripts:', error);
        }
    }

    addTranscriptToUI(transcript) {
        const container = document.getElementById('transcriptContainer');
        
        // Remove "no transcript" message if it exists
        const noTranscript = container.querySelector('.no-transcript');
        if (noTranscript) {
            noTranscript.remove();
        }
        
        const transcriptElement = document.createElement('div');
        transcriptElement.className = 'transcript-entry';
        transcriptElement.innerHTML = `
            <div class="transcript-header">
                <span class="speaker-name">${transcript.speakerName}</span>
                <span class="timestamp">${new Date(transcript.timestamp).toLocaleTimeString()}</span>
            </div>
            <div class="transcript-text">${transcript.text}</div>
            <div class="confidence-score">Confidence: ${Math.round(transcript.confidence * 100)}%</div>
        `;
        
        container.appendChild(transcriptElement);
        container.scrollTop = container.scrollHeight;
    }

    clearTranscript() {
        this.transcripts = [];
        const container = document.getElementById('transcriptContainer');
        container.innerHTML = '<div class="no-transcript">Transcript cleared. Start transcription to see new captions...</div>';
    }

    downloadTranscript() {
        if (this.transcripts.length === 0) {
            this.showError('No transcript available to download');
            return;
        }
        
        const transcriptData = {
            meeting: this.currentMeeting,
            transcripts: this.transcripts,
            generatedAt: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(transcriptData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `AccureMD_Transcript_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    startLiveUpdates() {
        // Poll for bot status updates
        setInterval(async () => {
            try {
                const response = await fetch('/api/meetings/status');
                const data = await response.json();
                
                // Update status display if needed
                console.log('Bot status:', data.status);
            } catch (error) {
                console.error('Error getting bot status:', error);
            }
        }, 10000); // Every 10 seconds
    }

    // UI Helper Methods
    hideLoading() {
        document.getElementById('loadingScreen').style.display = 'none';
    }

    showAuthScreen() {
        document.getElementById('authScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
    }

    showMainApp() {
        document.getElementById('authScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'flex';
    }

    updateUserInterface() {
        if (this.currentUser) {
            document.getElementById('userName').textContent = this.currentUser.userName || 'User';
            document.getElementById('userAvatar').textContent = (this.currentUser.userName || 'U').charAt(0).toUpperCase();
        }
    }

    showRecordingControls() {
        document.getElementById('recordingControls').style.display = 'block';
    }

    updateConnectionStatus(text, status) {
        const statusElement = document.getElementById('connectionStatus');
        const dot = statusElement.querySelector('.status-dot');
        const textElement = statusElement.querySelector('span:last-child');
        
        dot.className = `status-dot ${status}`;
        textElement.textContent = text;
    }

    updateRecordingInterface() {
        const startBtn = document.getElementById('startRecordingBtn');
        const stopBtn = document.getElementById('stopRecordingBtn');
        const indicator = document.getElementById('recordingIndicator');
        
        if (this.isRecording) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-block';
            indicator.textContent = '🔴 Recording';
            indicator.style.color = 'var(--recording-color)';
        } else {
            startBtn.style.display = 'inline-block';
            stopBtn.style.display = 'none';
            indicator.textContent = '⚪ Standby';
            indicator.style.color = 'var(--teams-text-secondary)';
        }
    }

    updateTranscriptionInterface() {
        const btn = document.getElementById('startTranscriptionBtn');
        if (this.isTranscribing) {
            btn.textContent = 'Transcribing...';
            btn.disabled = true;
            btn.style.backgroundColor = 'var(--success-color)';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 4px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            max-width: 300px;
            background-color: ${type === 'success' ? 'var(--success-color)' : 'var(--error-color)'};
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.accureMDApp = new AccureMDApp();
});'''

# Save CSS and JS files
with open('teams-app.css', 'w') as f:
    f.write(teams_css)

with open('teams-app.js', 'w') as f:
    f.write(teams_js)

print("✅ Created CSS and JavaScript files")
print("📄 teams-app.css - Complete styling for Teams app interface")
print("📄 teams-app.js - Full client-side functionality with Teams SDK integration")