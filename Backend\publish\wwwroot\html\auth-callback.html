<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Callback - AccureMD</title>
    <script src="https://res.cdn.office.net/teams-js/2.19.0/js/MicrosoftTeams.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .callback-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #6264A7;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .message {
            margin-top: 20px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="callback-container">
        <div class="spinner" id="spinner"></div>
        <h2>Processing Authentication...</h2>
        <div class="message" id="message">Please wait while we complete your sign-in.</div>
    </div>

    <script>
        console.log('AccureMD: Auth callback page loaded');

        // Helper function to parse URL hash parameters
        function getHashParameters() {
            const hash = window.location.hash.substring(1);
            const params = {};
            if (hash) {
                hash.split('&').forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        params[decodeURIComponent(key)] = decodeURIComponent(value);
                    }
                });
            }
            return params;
        }

        // Initialize Teams SDK
        microsoftTeams.app.initialize().then(() => {
            console.log('AccureMD: Teams SDK initialized in callback');

            // Parse the hash parameters from OAuth response
            const hashParams = getHashParameters();
            console.log('AccureMD: Hash parameters:', hashParams);

            // Check for error first
            if (hashParams.error) {
                console.error('AccureMD: OAuth error:', hashParams);
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = `<span class="error">❌ Authentication failed: ${hashParams.error_description || hashParams.error}</span>`;
                localStorage.setItem('accuremd.auth.error', JSON.stringify(hashParams));
                microsoftTeams.authentication.notifyFailure(hashParams.error_description || hashParams.error);
                return;
            }

            // Check for access token
            if (hashParams.access_token) {
                // Verify state parameter
                const expectedState = localStorage.getItem('accuremd.auth.state');
                if (expectedState && expectedState !== hashParams.state) {
                    console.error('AccureMD: State mismatch - expected:', expectedState, 'got:', hashParams.state);
                    document.getElementById('spinner').style.display = 'none';
                    document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: Security validation failed</span>';
                    localStorage.setItem('accuremd.auth.error', JSON.stringify({
                        error: 'state_mismatch',
                        error_description: 'State parameter does not match'
                    }));
                    microsoftTeams.authentication.notifyFailure('StateDoesNotMatch');
                    return;
                }

                console.log('AccureMD: Authentication successful, got access token');
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = '<span class="success">✅ Authentication successful! Closing window...</span>';

                // Prepare result object
                const result = {
                    accessToken: hashParams.access_token,
                    idToken: hashParams.id_token,
                    tokenType: hashParams.token_type,
                    expiresIn: hashParams.expires_in,
                    scope: hashParams.scope,
                    state: hashParams.state
                };

                // Store result in localStorage with a unique key
                const resultKey = 'accuremd.auth.result.' + Date.now();
                localStorage.setItem(resultKey, JSON.stringify(result));

                // Clean up state
                localStorage.removeItem('accuremd.auth.state');
                localStorage.removeItem('accuremd.auth.error');

                console.log('AccureMD: Notifying success with result key:', resultKey);

                // Notify Teams of success with the storage key
                microsoftTeams.authentication.notifySuccess(resultKey);

            } else {
                console.error('AccureMD: No access token found in callback');
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: No access token received</span>';
                localStorage.setItem('accuremd.auth.error', JSON.stringify({
                    error: 'no_token',
                    error_description: 'No access token received from OAuth provider'
                }));
                microsoftTeams.authentication.notifyFailure('UnexpectedFailure');
            }

        }).catch((error) => {
            console.error('AccureMD: Failed to initialize Teams SDK in callback:', error);
            document.getElementById('spinner').style.display = 'none';
            document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: Teams SDK initialization failed</span>';
            localStorage.setItem('accuremd.auth.error', JSON.stringify({
                error: 'teams_init_failed',
                error_description: 'Failed to initialize Teams SDK: ' + error.message
            }));

            // Try to notify failure even if Teams SDK failed
            try {
                microsoftTeams.authentication.notifyFailure('FailedToOpenWindow');
            } catch (e) {
                console.error('AccureMD: Could not notify Teams of failure:', e);
            }
        });
    </script>
</body>
</html>
