using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class TranscriptModel
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string MeetingId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string SpeakerId { get; set; } = string.Empty;
    public string SpeakerName { get; set; } = "Unknown";
    public string Text { get; set; } = string.Empty;
    public double Confidence { get; set; } = 0.0;
    public TimeSpan OffsetFromStart { get; set; }
}

public class LiveTranscriptionUpdate
{
    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;

    [JsonPropertyName("transcript")]
    public TranscriptModel Transcript { get; set; } = new();

    [JsonPropertyName("isPartial")]
    public bool IsPartial { get; set; } = false;
}

public class TranscriptionSettings
{
    public string Language { get; set; } = "en-US";
    public bool EnableSpeakerIdentification { get; set; } = true;
    public bool EnablePunctuation { get; set; } = true;
    public bool EnableWordLevelTimestamps { get; set; } = true;
}