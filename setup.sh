#!/bin/bash

# AccureMD Teams Bot Setup Script

echo "🚀 Setting up AccureMD Teams Bot..."

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET 8 SDK not found. Please install .NET 8 SDK first."
    exit 1
fi

if ! command -v ngrok &> /dev/null; then
    echo "⚠️  ngrok not found. Install ngrok for local development: npm install -g ngrok"
fi

echo "✅ Prerequisites check completed"

# Restore packages
echo "📦 Restoring NuGet packages..."
dotnet restore

if [ $? -eq 0 ]; then
    echo "✅ NuGet packages restored successfully"
else
    echo "❌ Failed to restore NuGet packages"
    exit 1
fi

# Build project
echo "🔨 Building project..."
dotnet build

if [ $? -eq 0 ]; then
    echo "✅ Project built successfully"
else
    echo "❌ Failed to build project"
    exit 1
fi

# Create directories
echo "📁 Creating required directories and placeholder files..."
mkdir -p Backend/recordings
mkdir -p Backend/storage/recordings
mkdir -p Backend/storage/transcripts
mkdir -p Backend/wwwroot/html
mkdir -p Backend/wwwroot/css
mkdir -p Backend/wwwroot/js

# Create placeholder UI files to ensure they are always published correctly
echo '<html><head><title>AccureMD</title><link rel="stylesheet" href="../css/teams-app.css"></head><body><h1>Welcome to AccureMD</h1><p>UI Loaded Successfully.</p><script src="../js/teams-app.js"></script></body></html>' > Backend/wwwroot/html/index.html
echo 'body { font-family: sans-serif; background-color: #f0f0f0; color: #333; }' > Backend/wwwroot/css/teams-app.css
echo 'console.log("AccureMD App UI Loaded");' > Backend/wwwroot/js/teams-app.js

# Setup configuration
echo "⚙️  Setting up configuration..."
if [ ! -f "Backend/appsettings.Production.json" ]; then
    cp Backend/appsettings.json Backend/appsettings.Production.json
    echo "📝 Created appsettings.Production.json - Please update with production values"
fi

# Generate sample icons for Teams manifest
echo "🎨 Creating sample icons..."
mkdir -p Backend/TeamsAppManifest
# This would typically create actual PNG files - here we note that they need to be created
echo "📝 Note: Please add color.png (192x192) and outline.png (32x32) to Backend/TeamsAppManifest folder"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update appsettings.json with your Bot Framework and Azure credentials"
echo "2. Update TeamsAppManifest/manifest.json with your Bot ID and domain"
echo "3. Create Teams app icons (color.png 192x192, outline.png 32x32)"
echo "4. Run 'dotnet run' to start the application"
echo "5. Use ngrok for local development: 'ngrok http 5000'"
echo "6. Update Bot Framework messaging endpoint with ngrok URL"
echo "7. Install the Teams app in your tenant"
echo ""
echo "📚 See README.md for detailed setup instructions"
