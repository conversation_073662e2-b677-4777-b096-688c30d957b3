# Create the main Program.cs file
program_cs = '''using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Connector.Authentication;
using AccureMD.TeamsBot.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddHttpClient();

// Configure Bot Framework
builder.Services.AddSingleton<IBotFrameworkHttpAdapter, AdapterWithErrorHandler>();
builder.Services.AddTransient<IBot, AccureMDBotService>();

// Add custom services
builder.Services.AddScoped<AuthenticationService>();
builder.Services.AddScoped<MeetingService>();
builder.Services.AddScoped<RecordingService>();
builder.Services.AddScoped<TranscriptionService>();
builder.Services.AddScoped<StorageService>();

// Add memory storage and conversation state
builder.Services.AddSingleton<IStorage, MemoryStorage>();
builder.Services.AddSingleton<ConversationState>();
builder.Services.AddSingleton<UserState>();

// Configure authentication
builder.Services.AddAuthentication();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();

/// <summary>
/// Error handler for Bot Framework
/// </summary>
public class AdapterWithErrorHandler : CloudAdapter
{
    public AdapterWithErrorHandler(BotFrameworkAuthentication auth, ILogger<AdapterWithErrorHandler> logger)
        : base(auth, logger)
    {
        OnTurnError = async (turnContext, exception) =>
        {
            logger.LogError(exception, "Exception caught : {ExceptionMessage}", exception.Message);
            
            // Send a message to the user
            await turnContext.SendActivityAsync("The bot encountered an error or bug.");
            await turnContext.SendActivityAsync("To continue to run this bot, please fix the bot source code.");
            
            // Send a trace activity, which will be displayed in the Bot Framework Emulator
            await turnContext.TraceActivityAsync("OnTurnError Trace", exception.Message, "https://www.botframework.com/schemas/error", "TurnError");
        };
    }
}'''

# Create the project file
project_file = '''<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>AccureMD.TeamsBot</AssemblyName>
    <RootNamespace>AccureMD.TeamsBot</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bot.Builder" Version="4.21.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.21.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Teams" Version="4.21.2" />
    <PackageReference Include="Microsoft.Graph" Version="5.32.0" />
    <PackageReference Include="Microsoft.Graph.Authentication" Version="2.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.18.0" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.34.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="TeamsAppManifest\\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>'''

# Save files
with open('Program.cs', 'w') as f:
    f.write(program_cs)

with open('AccureMD.TeamsBot.csproj', 'w') as f:
    f.write(project_file)

print("✅ Created Program.cs and project file")
print("📄 Program.cs - Main application entry point with Bot Framework setup")
print("📄 AccureMD.TeamsBot.csproj - Project file with all necessary NuGet packages")