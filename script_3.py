# Create Models
meeting_model = '''using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class MeetingModel
{
    public string Id { get; set; } = string.Empty;
    public string MeetingUrl { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; } = "Pending"; // Pending, Active, Completed, Error
    public string OrganizerId { get; set; } = string.Empty;
    public List<string> Participants { get; set; } = new();
    public string RecordingPath { get; set; } = string.Empty;
    public string TranscriptPath { get; set; } = string.Empty;
    public bool IsRecording { get; set; } = false;
    public bool IsTranscribing { get; set; } = false;
}

public class MeetingJoinRequest
{
    [JsonPropertyName("meetingUrl")]
    public string MeetingUrl { get; set; } = string.Empty;
    
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;
    
    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; } = "AccureMD Bot";
}

public class MeetingResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;
    
    [JsonPropertyName("joinUrl")]
    public string JoinUrl { get; set; } = string.Empty;
}'''

transcript_model = '''using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class TranscriptModel
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string MeetingId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string SpeakerId { get; set; } = string.Empty;
    public string SpeakerName { get; set; } = "Unknown";
    public string Text { get; set; } = string.Empty;
    public double Confidence { get; set; } = 0.0;
    public TimeSpan OffsetFromStart { get; set; }
}

public class LiveTranscriptionUpdate
{
    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;
    
    [JsonPropertyName("transcript")]
    public TranscriptModel Transcript { get; set; } = new();
    
    [JsonPropertyName("isPartial")]
    public bool IsPartial { get; set; } = false;
}

public class TranscriptionSettings
{
    public string Language { get; set; } = "en-US";
    public bool EnableSpeakerIdentification { get; set; } = true;
    public bool EnablePunctuation { get; set; } = true;
    public bool EnableWordLevelTimestamps { get; set; } = true;
}'''

auth_model = '''using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class AuthenticationModel
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime TokenExpiry { get; set; }
    public bool IsAuthenticated { get; set; } = false;
    public List<string> Permissions { get; set; } = new();
}

public class AuthenticationRequest
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;
    
    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;
    
    [JsonPropertyName("redirectUri")]
    public string RedirectUri { get; set; } = string.Empty;
}

public class AuthenticationResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    [JsonPropertyName("user")]
    public AuthenticationModel? User { get; set; }
    
    [JsonPropertyName("authUrl")]
    public string AuthUrl { get; set; } = string.Empty;
}'''

# Save model files
with open('MeetingModel.cs', 'w') as f:
    f.write(meeting_model)

with open('TranscriptModel.cs', 'w') as f:
    f.write(transcript_model)

with open('AuthenticationModel.cs', 'w') as f:
    f.write(auth_model)

print("✅ Created Model classes")
print("📄 MeetingModel.cs - Meeting data structures and API models")
print("📄 TranscriptModel.cs - Transcription data structures")
print("📄 AuthenticationModel.cs - Authentication and user data models")