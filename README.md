# AccureMD - Microsoft Teams AI Meeting Assistant

AccureMD is a comprehensive Microsoft Teams extension that functions as an AI-powered meeting assistant, similar to Read.AI. It provides meeting recording, live transcription, and guest meeting access capabilities.

## 🚀 Features

- **🎯 Guest Meeting Join**: Join Teams meetings as a bot without direct invitation
- **📹 Meeting Recording**: High-quality video and audio recording
- **📝 Live Transcription**: Real-time speech-to-text with speaker identification
- **🔐 Secure Authentication**: Microsoft Teams SSO integration
- **💾 Data Storage**: Configurable storage for recordings and transcripts
- **🎨 Teams UI Integration**: Native Teams sidebar interface

## 📋 Prerequisites

- .NET 8 SDK
- Visual Studio 2022 or VS Code
- Microsoft 365 Developer tenant
- Azure subscription (for deployment)
- Bot Framework registration
- Microsoft Graph API permissions

## 🛠️ Setup Instructions

### 1. Clone and Setup Project
```bash
# Navigate to project directory
cd AccureMD.TeamsBot

# Restore NuGet packages
dotnet restore

# Build the project
dotnet build
```

### 2. Configure Application Settings

Update `appsettings.json` with your configuration:

```json
{
  "MicrosoftAppId": "YOUR_BOT_APP_ID",
  "MicrosoftAppPassword": "YOUR_BOT_APP_SECRET",
  "BaseUrl": "https://your-domain.ngrok.io",
  "Teams": {
    "AppId": "YOUR_TEAMS_APP_ID",
    "AppSecret": "YOUR_TEAMS_APP_SECRET",
    "TenantId": "YOUR_TENANT_ID"
  },
  "TranscriptionService": {
    "ApiUrl": "YOUR_TRANSCRIPTION_API_URL",
    "ApiKey": "YOUR_TRANSCRIPTION_API_KEY"
  },
  "ConnectionStrings": {
    "DefaultConnection": "YOUR_DATABASE_CONNECTION",
    "StorageAccount": "YOUR_STORAGE_CONNECTION"
  }
}
```

### 3. Bot Framework Registration

1. Go to [Bot Framework Portal](https://dev.botframework.com/)
2. Create a new bot registration
3. Set messaging endpoint: `https://your-domain.com/api/messages`
4. Enable Teams channel
5. Configure calling capabilities

### 4. Teams App Registration

1. Open [Teams Developer Portal](https://dev.teams.microsoft.com/)
2. Create new app or import `TeamsAppManifest/manifest.json`
3. Update manifest with your Bot ID and domain
4. Configure required permissions:
   - OnlineMeetings.ReadWrite.All
   - CallRecords.Read.All
   - Calendars.Read
   - User.Read.All

### 5. Microsoft Graph API Setup

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to Azure Active Directory > App Registrations
3. Create new registration or use existing
4. Add API permissions:
   - Microsoft Graph > OnlineMeetings.ReadWrite.All
   - Microsoft Graph > CallRecords.Read.All
   - Microsoft Graph > Calendars.Read
   - Microsoft Graph > User.Read.All
5. Generate client secret
6. Configure redirect URIs

### 6. Local Development Setup

```bash
# Install ngrok for local testing
npm install -g ngrok

# Start ngrok tunnel
ngrok http 5000

# Update Bot Framework messaging endpoint with ngrok URL
# Update Teams app manifest with ngrok domain

# Run the application
dotnet run
```

### 7. Teams App Installation

1. Package the Teams app:
   - Navigate to the `Backend/TeamsAppManifest` folder.
   - Ensure you have `manifest.json`, `color.png` (192x192), and `outline.png` (32x32).
   - Select **only these three files**, right-click, and choose "Send to > Compressed (zipped) folder".
   - Name the resulting file `AccureMD.zip` (or similar). The manifest and icons must be at the root of the zip file.

2. Install in Teams:
   - Go to Teams Admin Center
   - Upload custom app or sideload for testing
   - Install the AccureMD app

## 🎮 Usage Instructions

### For End Users

1. **Open AccureMD in Teams**:
   - In a Teams meeting, click **Apps**
   - Search for and select **AccureMD**
   - The app will open in the meeting sidebar

2. **Authenticate**:
   - Click "Sign in with Microsoft"
   - Complete the authentication flow
   - Grant necessary permissions

3. **Join Meeting as Guest**:
   - Enter the Teams meeting URL in the input field
   - Click "Join as Guest"
   - AccureMD bot will join the meeting

4. **Start Recording/Transcription**:
   - Click "Start Recording" to begin video/audio capture
   - Click "Start Transcription" for live captions
   - Monitor real-time transcript in the sidebar

5. **Manage Session**:
   - Stop recording when finished
   - Download transcript if needed
   - Leave meeting to end session

### For Developers

#### API Endpoints

- `POST /api/auth/login` - Initiate authentication
- `POST /api/auth/callback` - Handle auth callback
- `GET /api/auth/status/{userId}` - Check auth status
- `POST /api/meetings/join` - Join meeting as guest
- `POST /api/meetings/{id}/recording/start` - Start recording
- `POST /api/meetings/{id}/recording/stop` - Stop recording
- `GET /api/meetings/{id}/transcripts` - Get live transcripts

#### Service Architecture

```
├── Controllers/
│   ├── BotController.cs       # Bot Framework endpoint
│   ├── AuthController.cs      # Authentication API
│   └── MeetingController.cs   # Meeting management API
├── Services/
│   ├── AccureMDBotService.cs  # Main bot logic
│   ├── AuthenticationService.cs # OAuth & Graph API
│   ├── MeetingService.cs      # Meeting management
│   ├── RecordingService.cs    # Recording functionality
│   ├── TranscriptionService.cs # Speech-to-text
│   └── StorageService.cs      # Data persistence
├── Models/
│   ├── MeetingModel.cs        # Meeting data structures
│   ├── TranscriptModel.cs     # Transcription models
│   └── AuthenticationModel.cs # Auth data models
└── wwwroot/
    ├── html/                  # Teams app UI
    ├── css/                   # Styling
    └── js/                    # Client-side logic
```

## 🔧 Configuration Options

### Recording Settings
- Storage path for recordings
- Maximum recording duration
- Supported file formats (MP4, WAV, MP3)

### Transcription Settings
- Speech API configuration
- Language support
- Speaker identification
- Confidence thresholds

### Storage Configuration
- Database connection strings
- Azure Storage settings
- Local file system options

## 🚀 Deployment

### Azure App Service Deployment

```bash
# Publish the application
dotnet publish -c Release -o ./publish/

# Zip the contents of the publish directory into a file (e.g., deploy.zip)
# On Windows: Open the publish folder, select all files, right-click -> Send to -> Compressed (zipped) folder.

# Deploy to Azure App Service
# Option 1: Use the Azure CLI (replace placeholders)
az webapp deployment source config-zip -g <YOUR_RESOURCE_GROUP> -n <YOUR_APP_SERVICE_NAME> --src deploy.zip

# Option 2: Drag and drop deploy.zip to https://<YOUR_APP_SERVICE_NAME>.scm.azurewebsites.net/ZipDeployUI

# After deployment, you may need to:
# Note on File Paths: The deployment process creates a `wwwroot` folder inside the server's application root (`D:\home\site\wwwroot`). This is expected.
# Your static files will be accessible at URLs like `https://<YOUR_APP_SERVICE_NAME>/html/index.html`, not `.../wwwroot/html/index.html`.

# Update Bot Framework endpoint
# Update Teams app manifest with production domain
```

### Docker Deployment

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY publish/ .
EXPOSE 80
ENTRYPOINT ["dotnet", "AccureMD.TeamsBot.dll"]
```

## 🔒 Security Considerations

- **Authentication**: All API calls require valid authentication
- **Permissions**: Minimal required Graph API permissions
- **Data Encryption**: Recordings encrypted at rest
- **Access Control**: User-based access to meeting data
- **Compliance**: GDPR-compliant data handling

## 📚 API Documentation

### Authentication Flow
1. User initiates login through Teams app
2. OAuth2 authorization code flow with Microsoft
3. Access token stored securely for Graph API calls
4. Token refresh handled automatically

### Meeting Management
1. Parse Teams meeting URL for meeting ID
2. Use Graph API to join meeting as application
3. Establish media streams for recording
4. Process audio for real-time transcription

### Data Storage
- Recordings stored in configured storage location
- Transcripts saved as structured JSON
- Metadata tracked in database
- User sessions managed in memory

## 🐛 Troubleshooting

### Common Issues

1. **Bot not responding**: Check Bot Framework registration and endpoint
2. **Authentication fails**: Verify app registration and permissions
3. **Can't join meetings**: Check Graph API permissions and scopes
4. **Recording not working**: Verify storage configuration and permissions
5. **Transcription issues**: Check speech service API configuration

### Debug Mode
Set `ASPNETCORE_ENVIRONMENT=Development` for detailed logging.

### Logs Location
- Application logs: Console output
- Bot Framework logs: Application Insights (if configured)
- Meeting logs: Custom logging service

## 📞 Support

For technical support or questions:
- Create an issue in the project repository
- Contact the development team
- Check Microsoft Teams Developer documentation

## 📄 License

This project is licensed under the MIT License. See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📈 Version History

- **v1.0.0**: Initial release with core functionality
  - Teams integration
  - Meeting recording
  - Live transcription
  - Guest access
  - Authentication

---

**AccureMD** - AI-Powered Meeting Assistant for Microsoft Teams
