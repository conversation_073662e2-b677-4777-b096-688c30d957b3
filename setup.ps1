# AccureMD Teams Bot Setup Script (PowerShell)

Write-Host "🚀 Setting up AccureMD Teams Bot..." -ForegroundColor Green

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
    Write-Host "❌ .NET 8 SDK not found. Please install .NET 8 SDK first." -ForegroundColor Red
    exit 1
}

if (-not (Get-Command "ngrok" -ErrorAction SilentlyContinue)) {
    Write-Host "⚠️  ngrok not found. Install ngrok for local development: npm install -g ngrok" -ForegroundColor Yellow
}

Write-Host "✅ Prerequisites check completed" -ForegroundColor Green

# Restore packages
Write-Host "📦 Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NuGet packages restored successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to restore NuGet packages" -ForegroundColor Red
    exit 1
}

# Build project
Write-Host "🔨 Building project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to build project" -ForegroundColor Red
    exit 1
}

# Create directories
Write-Host "📁 Creating required directories and placeholder files..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path ".\Backend\wwwroot\html" | Out-Null
New-Item -ItemType Directory -Force -Path ".\Backend\wwwroot\css" | Out-Null
New-Item -ItemType Directory -Force -Path ".\Backend\wwwroot\js" | Out-Null
New-Item -ItemType Directory -Force -Path ".\storage\recordings" | Out-Null
New-Item -ItemType Directory -Force -Path ".\storage\transcripts" | Out-Null

# Create placeholder UI files to ensure they are always published correctly
Set-Content -Path ".\Backend\wwwroot\html\index.html" -Value '<html><head><title>AccureMD</title><link rel="stylesheet" href="../css/teams-app.css"></head><body><h1>Welcome to AccureMD</h1><p>UI Loaded Successfully.</p><script src="../js/teams-app.js"></script></body></html>'
Set-Content -Path ".\Backend\wwwroot\css\teams-app.css" -Value 'body { font-family: sans-serif; background-color: #f0f0f0; color: #333; }'
Set-Content -Path ".\Backend\wwwroot\js\teams-app.js" -Value 'console.log("AccureMD App UI Loaded");'

# Create other required HTML files referenced in the manifest
Set-Content -Path ".\Backend\wwwroot\html\configure.html" -Value '<html><body><h1>Configure AccureMD</h1><p>Configuration page placeholder.</p></body></html>'
Set-Content -Path ".\Backend\wwwroot\html\privacy.html" -Value '<html><body><h1>Privacy Policy</h1><p>Privacy policy placeholder.</p></body></html>'
Set-Content -Path ".\Backend\wwwroot\html\termsofuse.html" -Value '<html><body><h1>Terms of Use</h1><p>Terms of use placeholder.</p></body></html>'

# Setup configuration
Write-Host "⚙️  Setting up configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".\Backend\appsettings.Production.json")) {
    Copy-Item ".\Backend\appsettings.json" ".\Backend\appsettings.Production.json"
    Write-Host "📝 Created appsettings.Production.json - Please update with production values" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Update appsettings.json with your Bot Framework and Azure credentials"
Write-Host "2. Update TeamsAppManifest/manifest.json with your Bot ID and domain"
Write-Host "3. Run 'dotnet run --project Backend' to start the application"
Write-Host "📚 See README.md for detailed setup instructions" -ForegroundColor Yellow