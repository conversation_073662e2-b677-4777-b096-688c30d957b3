import plotly.graph_objects as go

# Create figure
fig = go.Figure()

# Layer colors
layer_colors = {
    "Client": "#1FB8CD",     # <PERSON> cyan
    "Backend": "#DB4545",    # Bright red  
    "External": "#2E8B57"    # Sea green
}

# Define component positions and sizes
components = [
    # Client Layer (Top)
    {"name": "Teams Client", "x": 0.5, "y": 4.5, "width": 8, "height": 0.8, "layer": "Client"},
    {"name": "AccureMD UI", "x": 1, "y": 3.8, "width": 1.8, "height": 0.4, "layer": "Client"},
    {"name": "Meeting Ctrl", "x": 3, "y": 3.8, "width": 1.8, "height": 0.4, "layer": "Client"},
    {"name": "Transcript", "x": 5, "y": 3.8, "width": 1.8, "height": 0.4, "layer": "Client"},
    {"name": "Auth Panel", "x": 7, "y": 3.8, "width": 1.8, "height": 0.4, "layer": "Client"},
    
    # Backend Layer (Middle)
    {"name": ".NET Backend", "x": 0.5, "y": 2.8, "width": 8, "height": 0.8, "layer": "Backend"},
    {"name": "BotController", "x": 0.8, "y": 2.1, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "AuthController", "x": 2.5, "y": 2.1, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "MeetingCtrl", "x": 4.2, "y": 2.1, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "StorageCtrl", "x": 5.9, "y": 2.1, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "AuthService", "x": 0.8, "y": 1.5, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "MeetingSvc", "x": 2.5, "y": 1.5, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "RecordingSvc", "x": 4.2, "y": 1.5, "width": 1.5, "height": 0.4, "layer": "Backend"},
    {"name": "TranscriptSvc", "x": 5.9, "y": 1.5, "width": 1.5, "height": 0.4, "layer": "Backend"},
    
    # External Layer (Bottom)
    {"name": "External APIs", "x": 0.5, "y": 0.8, "width": 8, "height": 0.8, "layer": "External"},
    {"name": "Graph API", "x": 0.8, "y": 0.1, "width": 1.5, "height": 0.4, "layer": "External"},
    {"name": "Teams API", "x": 2.5, "y": 0.1, "width": 1.5, "height": 0.4, "layer": "External"},
    {"name": "Speech API", "x": 4.2, "y": 0.1, "width": 1.5, "height": 0.4, "layer": "External"},
    {"name": "Database", "x": 5.9, "y": 0.1, "width": 1.5, "height": 0.4, "layer": "External"},
    {"name": "Storage", "x": 7.6, "y": 0.1, "width": 1.5, "height": 0.4, "layer": "External"}
]

# Add rectangles for each component
for comp in components:
    fig.add_shape(
        type="rect",
        x0=comp["x"], y0=comp["y"],
        x1=comp["x"] + comp["width"], y1=comp["y"] + comp["height"],
        fillcolor=layer_colors[comp["layer"]],
        line=dict(color="white", width=2),
        opacity=0.8
    )
    
    # Add text labels
    fig.add_annotation(
        x=comp["x"] + comp["width"]/2,
        y=comp["y"] + comp["height"]/2,
        text=comp["name"],
        showarrow=False,
        font=dict(color="white", size=10),
        xanchor="center",
        yanchor="middle"
    )

# Add arrows for key connections
arrows = [
    {"x0": 4.5, "y0": 3.8, "x1": 4.5, "y1": 3.6},  # Client to Backend
    {"x0": 1.5, "y0": 1.5, "x1": 1.5, "y1": 1.2},  # Auth to Graph
    {"x0": 3.2, "y0": 1.5, "x1": 3.2, "y1": 1.2},  # Meeting to Teams
    {"x0": 4.9, "y0": 1.5, "x1": 4.9, "y1": 1.2},  # Recording to Speech
    {"x0": 6.6, "y0": 1.5, "x1": 6.6, "y1": 1.2}   # Transcript to Storage
]

for arrow in arrows:
    fig.add_annotation(
        x=arrow["x1"], y=arrow["y1"],
        ax=arrow["x0"], ay=arrow["y0"],
        arrowhead=2,
        arrowsize=1,
        arrowwidth=2,
        arrowcolor="gray",
        showarrow=True
    )

# Update layout
fig.update_layout(
    title="AccureMD Teams Architecture",
    showlegend=False,
    plot_bgcolor='white'
)

# Update axes
fig.update_xaxes(
    showgrid=False,
    zeroline=False,
    showticklabels=False,
    title="",
    range=[-0.2, 9.5]
)

fig.update_yaxes(
    showgrid=False,
    zeroline=False,
    showticklabels=False,
    title="",
    range=[-0.2, 5.5]
)

# Save the chart
fig.write_image("architecture_diagram.png", width=1200, height=700)