<!DOCTYPE html>
<html>
<head>
    <title>AccureMD - Authentication Start</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.19.0/js/MicrosoftTeams.min.js"></script>
</head>
<body>
    <div style="text-align: center; padding: 20px;">
        <h2>Redirecting to Microsoft Sign-in...</h2>
        <p>Please wait while we redirect you to Microsoft for authentication.</p>
        <div style="margin: 20px;">
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
    </style>

    <script>
        console.log('AccureMD: Auth start page loaded');

        // Helper function to generate GUID
        function generateGuid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // Helper function to convert object to query string
        function toQueryString(obj) {
            return Object.keys(obj).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key])).join('&');
        }

        // Initialize Teams SDK and start authentication
        microsoftTeams.app.initialize().then(() => {
            console.log('AccureMD: Teams SDK initialized in auth start page');
            
            // Get Teams context
            microsoftTeams.app.getContext().then((context) => {
                console.log('AccureMD: Got Teams context:', context);
                
                // Generate random state string and store it
                const state = generateGuid();
                localStorage.setItem('accuremd.auth.state', state);
                localStorage.removeItem('accuremd.auth.error');
                
                console.log('AccureMD: Generated state:', state);
                
                // Build OAuth URL parameters
                const queryParams = {
                    client_id: '24a397f4-16dd-4dae-8b8f-5368c3a81fed',
                    response_type: 'id_token token',
                    response_mode: 'fragment',
                    scope: 'https://graph.microsoft.com/User.Read https://graph.microsoft.com/OnlineMeetings.ReadWrite https://graph.microsoft.com/Calendars.Read openid profile',
                    redirect_uri: window.location.origin + '/html/auth-callback.html',
                    nonce: generateGuid(),
                    state: state,
                    login_hint: context.user?.loginHint || context.user?.userPrincipalName || ''
                };
                
                // Build the authorization endpoint URL
                const tenantId = context.user?.tenant?.id || 'common';
                const authorizeEndpoint = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?${toQueryString(queryParams)}`;
                
                console.log('AccureMD: Redirecting to OAuth endpoint:', authorizeEndpoint);
                
                // Redirect to Microsoft OAuth
                window.location.assign(authorizeEndpoint);
                
            }).catch((error) => {
                console.error('AccureMD: Failed to get Teams context:', error);
                localStorage.setItem('accuremd.auth.error', JSON.stringify({
                    error: 'context_failed',
                    error_description: 'Failed to get Teams context: ' + error.message
                }));
                // Redirect to callback with error
                window.location.assign(window.location.origin + '/html/auth-callback.html');
            });
            
        }).catch((error) => {
            console.error('AccureMD: Failed to initialize Teams SDK:', error);
            localStorage.setItem('accuremd.auth.error', JSON.stringify({
                error: 'teams_init_failed',
                error_description: 'Failed to initialize Teams SDK: ' + error.message
            }));
            // Redirect to callback with error
            window.location.assign(window.location.origin + '/html/auth-callback.html');
        });
    </script>
</body>
</html>
