{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "css/teams-app.1i87atu4yk.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1i87atu4yk"}, {"Name": "integrity", "Value": "sha256-yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI="}, {"Name": "label", "Value": "css/teams-app.css"}]}, {"Route": "css/teams-app.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI="}]}, {"Route": "html/auth-callback.302we0abct.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6947"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IwDBxvasTrw/x1KuJWe9HI2CMBSGjKu/T9HuwSHglFk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:46:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "302we0abct"}, {"Name": "integrity", "Value": "sha256-IwDBxvasTrw/x1KuJWe9HI2CMBSGjKu/T9HuwSHglFk="}, {"Name": "label", "Value": "html/auth-callback.html"}]}, {"Route": "html/auth-callback.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6947"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IwDBxvasTrw/x1KuJWe9HI2CMBSGjKu/T9HuwSHglFk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:46:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IwDBxvasTrw/x1KuJWe9HI2CMBSGjKu/T9HuwSHglFk="}]}, {"Route": "html/configure.8inynrhuf2.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inynrhuf2"}, {"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}, {"Name": "label", "Value": "html/configure.html"}]}, {"Route": "html/configure.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 21:24:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/index.9rv7a2k3wu.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 10:49:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9rv7a2k3wu"}, {"Name": "integrity", "Value": "sha256-hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ="}, {"Name": "label", "Value": "html/index.html"}]}, {"Route": "html/index.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6452"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 10:49:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}, {"Name": "label", "Value": "html/privacy.html"}]}, {"Route": "html/privacy.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/termsofuse.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}, {"Name": "label", "Value": "html/termsofuse.html"}]}, {"Route": "js/teams-app.a3srro0ai3.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22109"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Cl48VDiHAJ3EQvkWU4AeaP9Mi54I+GwnyrzcinVWZ2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:46:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a3srro0ai3"}, {"Name": "integrity", "Value": "sha256-Cl48VDiHAJ3EQvkWU4AeaP9Mi54I+GwnyrzcinVWZ2U="}, {"Name": "label", "Value": "js/teams-app.js"}]}, {"Route": "js/teams-app.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22109"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Cl48VDiHAJ3EQvkWU4AeaP9Mi54I+GwnyrzcinVWZ2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:46:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cl48VDiHAJ3EQvkWU4AeaP9Mi54I+GwnyrzcinVWZ2U="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}, {"Name": "label", "Value": "test-auth.html"}]}, {"Route": "test-auth.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}]}