<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
</head>
<body>
    <h1>Authentication Test</h1>
    <button id="testAuth">Test Authentication</button>
    <div id="result"></div>

    <script>
        document.getElementById('testAuth').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing authentication...';

            try {
                console.log('Starting authentication test...');
                
                // Test the login endpoint
                const loginUrl = '/api/auth/login?userId=test-user&redirectUri=' + encodeURIComponent(window.location.origin + '/html/auth-callback.html');
                console.log('Calling login URL:', loginUrl);
                
                const response = await fetch(loginUrl);
                console.log('Login response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const authData = await response.json();
                console.log('Auth data received:', authData);
                
                if (authData.success && authData.authUrl) {
                    resultDiv.innerHTML = `
                        <p>Authentication URL generated successfully!</p>
                        <p><a href="${authData.authUrl}" target="_blank">Click here to authenticate</a></p>
                        <p>Auth URL: ${authData.authUrl}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>Error: ${authData.message}</p>`;
                }
                
            } catch (error) {
                console.error('Authentication test error:', error);
                resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
