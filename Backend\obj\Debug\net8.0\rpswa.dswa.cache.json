{"GlobalPropertiesHash": "ITtAFkNiSgdhUb2BwCQUf6iyFsK/CwOgeB9JuCIv+uU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["EiYgeK27Qw6p3F+UPl0qSB/kzD0wtPLix7cvoSIW2iU=", "2ksoq0X2CTBZKQZS3KRejyaRosJ8pTNwvEnVNsrn/mo=", "GhmdfXk1MMEUAuhYFy173QvKEoqrWtp23yZ1uUVfMSM=", "mtcBaC/bj66Y4LxfLCdcpasxXAvQQwZrcor5u2jHQfA=", "5//gAy/kH2TrsxDuKcytgxOFFXwEax7+zwOnmnTxFYE=", "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=", "H7OSqKmZxEHbmWj0cAttbItAQ1EijZ1QVr3kowUQwH0=", "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=", "/sfuOge2fAksFDJ+5Kp2ewiuE0hEp8LriomaHd8NmGA=", "e+0fbT/hAj7jxzS7j+9v4qKk4I21XVy5b/pqvhaljak=", "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=", "l2PEG+Gvik7flkoH7RtTb1jj+Gw6ex72ArroIzqb6Z4=", "mt1HXH1mr0AP45LRcRwnNsuwkpizvQHKWizEjvBjdic=", "FMCIPse1PmQ58cxdVafVp43T9TqFDYwDLqdQhRO08F4=", "KmA6Vec0Bgik8+xVFjZXj5i8GLIeWxxWK4JwWsjh5jM=", "ysxyai2UeIK4If/LFgOzGmTrs3x7gMj8YtvEjnjXy88=", "DP1FLFTBS5rPrTosVDQDSy2OS+qRWHvd4zMJ1iQ6lKM=", "54FHDfrl8SFKVkfn/DbiO+cLCT6bbVEG7dG8vhnM/wA=", "Sbvm2U5DA8irXUVE1XkxlBHVOXQT3qp8RfWX17Mp3QQ=", "ScYki9iwEB238+AZmetiywWVNQBeTRfDcJPkiraA/jI=", "hxzFe+p3v4owYCvkFxKUm05SqiYwRv/PnU7c+xR8yQY=", "eWVkZYdqJAGReCHkXWGcBGQoxHEoEppAOTUMiaYKCNc=", "hA9sHSHxMI7FAnR6YdDAXptktdG04gdoddNyITvdE+A=", "L956wLdG4lL2koHc4qQKU5FVm124soM+s6YSJjQYcgM=", "qSAJVNPsrer7SvA6nFooEOl1RnQdlm3HE3do+gqtDfA=", "qFlo+EWtmLm+4U7Gxv51oXPqVQeMlk06V4S/Hpw/l8A=", "jXe8u9EcawqGf88DGXkdzKhAToMOHUqGeLa++Kjewro=", "bjhmkAOD5ycysjNspMn0Tf9MTC+FO4mNVQ4qSHIzHs8=", "/87ne7mi9k48sc3GDXkbuKQNiL7UhrkDXzCS3vk6gtA=", "9VcVaJirff2Qt/uI6PzD6fvtuUWTD9DsxUC6aWYQ3cY=", "2zs207MjfPqE0Ayb1vqu1rdEMT8ExxjdHtdfWC0eXs8=", "DHnEM66g98FxbVd0ap+a93J3/rFpAsKAy1W6DOMZ/kk=", "UlnyY426PdkrxaYlto9F28BepMgz9//HXAxgE+bpfwU=", "gy5uJ0RwEHFaOCEDCcxPCHNv2RdOQjNlRYPlgoJruEE=", "v+Sx4C/D1PNFkFuZ6dg0lonvawq+2z0BPCqFYUAmVZo=", "R5OWUHt4NONn8d5T4AGUdtoQLQbc2ELtN7HnbkXd7Js=", "Bv/dhehd6UMGRaS62yXA9EuMLy/WQwer9Mnptkn5pPE=", "ft5D6n3cgfduNdv/NhJqglROo8Fb673I+BRyyKdAZG0=", "EHDXuUo7A5bfWtF06k9pu3jhPClBsDQ0g8hRn7hfhTs=", "7xBn3+l3yapZbbZ7XK0ZNWQDddjYXXe5XOijTpWCzgw=", "R5J6R8WLyuvSpgEbc+HV3B1M1c9bRXqyWTsvkP8ndtU=", "QLUvombc/kBW9R1v9WYIm7C+CVn9SmSCCVGjlVxCQeI=", "QQVyaVMM5jMq5pNuLpmFWmsM1CWxRRQJmZjz/Vm1cs8=", "NnNkKbR/wUf9//q4ATO0rcFkfSeO+z69Qso3nteLk8M=", "gi60nAyLf2yK8QAS1UO0ZOaQqAHXT+KyLXIMWPZEJWc=", "YWDpw39Zg5g7R32wfnbY1Z+w+yHd3xd2PK+LFklOlg0=", "ZpzbpJO4ytVMaxMdUCB0x36c3naGKrvX1fnTg9mPaDM=", "nZY3r+1iK1399iGOiQUaD2PbBiqHPYI4oG8mmPa5obk=", "H+2EPQGnFq8fEQUku1Wkzz0FPR1J39JkC5zbWgga0zo=", "gZq2kWSpcaAHp01jhfzg1wWIEcl3kFr4/ONme+wGL64=", "AXNjBtGJ55FLPeijtwpJK8i2vFJfLUKYAginv1a7Ufg=", "unVC5V/wv2o2n+1l5jvmoVjwwr3EPQZBPERL4xXJRQs=", "+pYTklN7fGxRhVoZGvMPNoXZKwd4eGfvl89lIa9yZOM=", "H09Tl1FXQoaRTW0dGH8tiHVbDwYikivaiOKUq9WitaE=", "NKJVnvIAOolR3laSCxb0MvkzjxP2NCcvCNF3oXHoA1A=", "DGvKmv09QMxcGjBXgya1cIOgkylGiJB9u57MLXQmbEc=", "pGUGe64JwyQkyFCWpehRFugpBlDSQwxQ9ZbtNfnUaSg=", "fi9e1gME2mIfhTVWzNIVUY4Jj6ANyToweJElqufMfBw=", "M+ysDKPerrCKuEQ0MZcoLbiVZTf4woZ0AFZj4hCN7RI=", "ZXuvsZIlHFFEbvDjCipIUhdUWcGOMxntniP8sTIy+kM=", "zJFnS9uu1YVBaUaSt28y9iB40ehqO+RJhM7GeWXhElE=", "Rr4fjhRq/qve98kd2VZskLGCgk1ld6JI9aFIkZ479m4=", "eAE+gugD/y6w+4eAAJta+B8afxyXeHGZM5E6wM9cRoI=", "NBTK359OuzkBG/8C8Xm8dYB/fZ2MWTdjNiFJvvFpbfo=", "9RjBrSDi3GqyTr1jfVFD9QNyNror3q0ho7LLTEPyils=", "ik9bgwvSBU3teylvUISd0gw8WzlXVbrxNNHLD/5cXI4=", "2pA/o6qZpkKIOVcGMoUGbYf3Z/O6v4g45dABlWcj4wY=", "Si1UMNwGaMIz5TOofyXq9janFiULsf3jpKvAC1uHBT8=", "EhxGq3jMU9mlcLJeg7DOvHKaY1awylMfDXbe9Moff/E=", "p5AOPIfSYkIp1FRQmW3dUeLlIYt2qHFXC/LoNSY4HC4=", "/ucrbrWRcuaUHxVQ+cFVM3oqDIFn14keOd44+7mpgfQ=", "2f1NldBVtMyZin1SCHdyTNMZqgCI8t9hCmO95TlU+S8=", "wG9lEVdayQlVS+Fjx11tzIuo9JRarxFS+F/aHvI2luE=", "9/RB/E4r+Ok76o9dFwxBKGSWPD8fvp7F89C1tFDDNCA=", "Kt1Utk5QcqOgzWOnY0RXlkMeJpi19W0nTVxutsHebMc=", "40n+EzCI9u12TyCs9SaDT4nn6IojvKtW7jap2ndw9ms=", "RCG21AQ/VV9crGWqnWZnDb6rdb0DOt2Jjz7DuoYqLEU=", "ez5gQq5re7BQ1moguuiYlM3xb0+Z6oPCkqk5Pj2m3cw=", "LIYTLHxtg4mQMIOeTf7/juGQLTdKUf+m5u439HtqGsg=", "O4V90JEwaXw9tAddfRyHFeVNIZj+LXcLcZfwQ0F1/3I=", "/Rn8aUoW9SBrT68AUl1j2p+Bimgr+skunnfvI/Z/WGs=", "<PERSON><PERSON><PERSON><PERSON>+/wADo/oks3+SjGFLq95k0bX9ZXC3fFpz6xY8=", "9N46qo4m79wGUjFNhVH1rm4ThQtrzq5s3Zq1HE6Q3Pg=", "Q4N22kcnMZ14+LlPSJOevC6/jYq9aMzzTOlaKxcKfgI=", "kO4kLlPyKxLJ0kb97hptBJ2pTnRxcZLN1koyAGQStrU=", "rFuFXYNlP68d+gBGmf9zqZcSWU6uTUuBwXASxMJuQAs=", "D8fUAB0BHEigT9sUIngkDEY3H7GBtmfrF4drcR7BxD4=", "Au59kx0TkJ4QXGLUKLDIk5kdlzOhn834ghGY/TjMra8=", "0Ad0VNFLCZzOquSiuMKOrIq0ojC5oYlltvDVkHQvZpI=", "EGHZEOHMrhCH4PlmLeofySdL54MsV3GgAKhsUgA3FeY=", "e7UMod7XGVRhrNP64mD14fWawcbqXEHHfa46/YfHAkk=", "/i5jW+Nx8hV1WlLOwjgEDPK6HfuTv3d0P+QUqrM8EcM=", "NMt7LhtMa5OV15AK2bWRMEP1SbYSd+9XiOKO2YQw3bw=", "JOyNszNmwlSOIGAeXaBrfbmHsPadvvpXReL6IspCCdQ=", "UNZkEjE3G2rLFL5HIDAIaLSjeLqXcXt8GsBeMKc8HJ8=", "4lUvtyHJ5AcBPOBFW7lWgoqp0lvIryxAK7xFavkDego=", "PWUA30MX8K3xMKEPoUOF4ry1Kh45VxJ9HsQ/6GBPPQE=", "lIdJRrohL6RbE2DFH9zM3z7UOI19aqf3rGnuxwGD75A=", "cRbn18LlHXtk7RkCoSBsByzBAW3DtXlIOJR/Q3XUGgQ=", "AhqikOv4467TXIua7OyxrgSaZQPuNUVqUB3p5u6m2VQ=", "gJPia7L0S1Q8tFE4FekbsYeyCPUSkfSpcnrjytH4D50=", "wgb7BOuaLGMDLAfGyy3eWEvoGx/6qbP1aXgKW4tcMeY=", "kdPHbHzh0EQka7R6HMXA/IqVmjC5WGQz5wChC+83xYI=", "dbfXeHO1ETS3ylrRgit8ivjK15T2hAU2EviXCAIgQwo=", "+xNSoY0aRhwJz7kNdb4B2pUGG3+Ls6fUWbf1P2V9WLg=", "f1zb8G3bnpM6/8/v0pj41yuSOBVNJLDfKKFlxp1OfLk=", "z4q+3kuacg9RpL9EcsgYlew6qfRJHUAD0kGZqlWvS+c=", "hf2Y7s+a5w6RTnXN9H9iTMA0X26dWAFt2evCUIsQAu0=", "1KVx8tsq2YgjA30O8EJbYYDawPRon62jzRzR/6ejvtM=", "u38Wgix3FLuSXCqGVPlK0mAgKW2RwqXCkVgMjlHYuOs=", "5G1J1SZqyeMOu2PY5ocsAWV3VRTtR3dSiPz0ohpJLfw=", "uFpHCUg54p11Hjvj9XZ0Kua1EEwPz1wBy+R//mne3VM="], "CachedAssets": {"EiYgeK27Qw6p3F+UPl0qSB/kzD0wtPLix7cvoSIW2iU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1i87atu4yk", "Integrity": "yeQ4DP3Liwchzu7/su/cMkA11DS5fWkMcBaazEN+aVI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 9483, "LastWriteTime": "2025-08-07T21:24:30.0835034+00:00"}, "2ksoq0X2CTBZKQZS3KRejyaRosJ8pTNwvEnVNsrn/mo=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4m0ksaowe9", "Integrity": "HMRZFNuOiKIXAggT9gstYEnFsuwzv1x+DRgF5xmQBHk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 7160, "LastWriteTime": "2025-08-08T16:43:38.6880165+00:00"}, "mtcBaC/bj66Y4LxfLCdcpasxXAvQQwZrcor5u2jHQfA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "77xizkbh7e", "Integrity": "bcQlLquRtIuxDPRyUv3DOvwoxUab2sL3l84QX2k4bZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4122, "LastWriteTime": "2025-08-08T17:03:50.5087965+00:00"}, "GhmdfXk1MMEUAuhYFy173QvKEoqrWtp23yZ1uUVfMSM=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0k2wjtqfvf", "Integrity": "EEQVpFSBICKPOKcSMbvitq3h8zWGHXeKeMmeHtMKMEk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 4697, "LastWriteTime": "2025-08-08T16:42:10.79647+00:00"}, "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26.6394667+00:00"}, "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34.6547862+00:00"}, "5//gAy/kH2TrsxDuKcytgxOFFXwEax7+zwOnmnTxFYE=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9rv7a2k3wu", "Integrity": "hS5LyYdoMdZysD7WaenqzYH6AK8hoBaTk9MWAscAEeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6452, "LastWriteTime": "2025-08-08T10:49:00.7142945+00:00"}, "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16.6719653+00:00"}, "/sfuOge2fAksFDJ+5Kp2ewiuE0hEp8LriomaHd8NmGA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gpjzragfzl", "Integrity": "fI7Kb2nnBJChbK1YSi2gDeVodzOFxzEu/32CKiFWcoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 23815, "LastWriteTime": "2025-08-08T16:44:38.8006992+00:00"}, "H7OSqKmZxEHbmWj0cAttbItAQ1EijZ1QVr3kowUQwH0=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\remove.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/remove#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "klzr35bbdh", "Integrity": "CIxWwzwEupnHIvQNT4ohs2hnrn3o32JdPKDoTSNZga8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\remove.html", "FileLength": 2580, "LastWriteTime": "2025-08-08T17:04:10.234825+00:00"}, "e+0fbT/hAj7jxzS7j+9v4qKk4I21XVy5b/pqvhaljak=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5n4anau1vc", "Integrity": "ATUgHTyDuxcR8JW6ho6eyb8kEBXbzeQ3mm99abbdGKE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 5970, "LastWriteTime": "2025-08-08T16:46:34.5473388+00:00"}}, "CachedCopyCandidates": {}}