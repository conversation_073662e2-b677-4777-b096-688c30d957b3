# Create Teams App Manifest
teams_manifest = '''{
  "$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json",
  "manifestVersion": "1.16",
  "version": "1.0.0",
  "id": "com.accureMD.teamsbot",
  "packageName": "com.accureMD.teamsbot",
  "developer": {
    "name": "AccureMD",
    "websiteUrl": "https://accureMD.com",
    "privacyUrl": "https://accureMD.com/privacy",
    "termsOfUseUrl": "https://accureMD.com/terms"
  },
  "icons": {
    "color": "color.png",
    "outline": "outline.png"
  },
  "name": {
    "short": "AccureMD",
    "full": "AccureMD Meeting Assistant"
  },
  "description": {
    "short": "AI-powered meeting recording and transcription assistant",
    "full": "AccureMD provides live transcription, meeting recording, and AI-powered insights for Microsoft Teams meetings. Join meetings as a guest, record audio and video, and get real-time transcripts."
  },
  "accentColor": "#6264A7",
  "bots": [
    {
      "botId": "{{MICROSOFT_APP_ID}}",
      "scopes": [
        "personal",
        "team",
        "groupchat"
      ],
      "supportsFiles": false,
      "isNotificationOnly": false,
      "supportsCalling": true,
      "supportsVideo": true
    }
  ],
  "configurableTabs": [
    {
      "configurationUrl": "{{BASE_URL}}/configure.html",
      "canUpdateConfiguration": true,
      "scopes": [
        "team",
        "groupchat"
      ],
      "context": [
        "meetingChatTab",
        "meetingSidePanel"
      ]
    }
  ],
  "staticTabs": [
    {
      "entityId": "accureMD-tab",
      "name": "AccureMD",
      "contentUrl": "{{BASE_URL}}/index.html",
      "websiteUrl": "{{BASE_URL}}/index.html",
      "scopes": [
        "personal"
      ]
    }
  ],
  "permissions": [
    "identity",
    "messageTeamMembers"
  ],
  "validDomains": [
    "{{BASE_DOMAIN}}",
    "login.microsoftonline.com",
    "graph.microsoft.com"
  ],
  "webApplicationInfo": {
    "id": "{{MICROSOFT_APP_ID}}",
    "resource": "https://graph.microsoft.com"
  },
  "authorization": {
    "permissions": {
      "resourceSpecific": [
        {
          "name": "OnlineMeetings.ReadWrite.Chat",
          "type": "Application"
        },
        {
          "name": "Calls.JoinGroupCall.Chat",
          "type": "Application"
        }
      ]
    }
  }
}'''

# Teams App HTML Files
configure_html = '''<!DOCTYPE html>
<html>
<head>
    <title>AccureMD Configuration</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <link rel="stylesheet" href="/css/teams-app.css">
</head>
<body class="configure-page">
    <div class="container">
        <div class="header">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzYyNjRBNyIvPgo8cGF0aCBkPSJNOCAxMkg24CIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTggMThIMjQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik04IDI0SDI0IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="AccureMD" class="logo">
            <h1>AccureMD Configuration</h1>
            <p>Configure your AI meeting assistant</p>
        </div>

        <div class="config-form">
            <div class="form-group">
                <label for="botName">Bot Display Name:</label>
                <input type="text" id="botName" value="AccureMD Assistant" />
            </div>
            
            <div class="form-group">
                <label for="autoRecord">Auto-start recording:</label>
                <input type="checkbox" id="autoRecord" />
            </div>
            
            <div class="form-group">
                <label for="autoTranscribe">Auto-start transcription:</label>
                <input type="checkbox" id="autoTranscribe" checked />
            </div>
            
            <div class="form-group">
                <label for="language">Transcription Language:</label>
                <select id="language">
                    <option value="en-US">English (US)</option>
                    <option value="en-GB">English (UK)</option>
                    <option value="es-ES">Spanish</option>
                    <option value="fr-FR">French</option>
                    <option value="de-DE">German</option>
                </select>
            </div>
        </div>

        <div class="actions">
            <button id="saveBtn" class="primary-btn">Save Configuration</button>
            <button id="cancelBtn" class="secondary-btn">Cancel</button>
        </div>
    </div>

    <script>
        microsoftTeams.app.initialize().then(() => {
            microsoftTeams.pages.config.registerOnSaveHandler((saveEvent) => {
                const config = {
                    botName: document.getElementById('botName').value,
                    autoRecord: document.getElementById('autoRecord').checked,
                    autoTranscribe: document.getElementById('autoTranscribe').checked,
                    language: document.getElementById('language').value
                };

                microsoftTeams.pages.config.setConfig({
                    suggestedDisplayName: 'AccureMD',
                    contentUrl: window.location.origin + '/index.html',
                    websiteUrl: window.location.origin + '/index.html',
                    entityId: 'accureMD-config',
                    removeUrl: window.location.origin + '/remove.html'
                }).then((result) => {
                    saveEvent.notifySuccess();
                }).catch((error) => {
                    console.error('Error saving config:', error);
                    saveEvent.notifyFailure('Failed to save configuration');
                });
            });

            microsoftTeams.pages.config.setValidityState(true);

            document.getElementById('saveBtn').addEventListener('click', () => {
                microsoftTeams.pages.config.registerOnSaveHandler();
            });

            document.getElementById('cancelBtn').addEventListener('click', () => {
                microsoftTeams.pages.config.setValidityState(false);
            });
        });
    </script>
</body>
</html>'''

# Main Teams App Interface
index_html = '''<!DOCTYPE html>
<html>
<head>
    <title>AccureMD - AI Meeting Assistant</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <link rel="stylesheet" href="/css/teams-app.css">
</head>
<body class="main-app">
    <div id="loadingScreen" class="loading-screen">
        <div class="loader"></div>
        <p>Loading AccureMD...</p>
    </div>

    <div id="authScreen" class="auth-screen" style="display: none;">
        <div class="auth-container">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzYyNjRBNyIvPgo8cGF0aCBkPSJNMTYgMjRINDgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xNiAzNkg0OCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSI0IiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTE2IDQ4SDQ4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="AccureMD" class="logo-large">
            <h1>Welcome to AccureMD</h1>
            <p>Your AI-powered meeting assistant for Microsoft Teams</p>
            <button id="loginBtn" class="login-btn">
                <span class="ms-icon"></span>
                Sign in with Microsoft
            </button>
        </div>
    </div>

    <div id="mainApp" class="main-app-container" style="display: none;">
        <div class="header">
            <div class="user-info">
                <div class="avatar" id="userAvatar"></div>
                <span id="userName">User</span>
                <button id="logoutBtn" class="logout-btn">Sign Out</button>
            </div>
        </div>

        <div class="content">
            <div class="meeting-controls">
                <h2>🎯 Join Meeting</h2>
                <div class="input-group">
                    <label for="meetingUrl">Teams Meeting URL:</label>
                    <input type="url" id="meetingUrl" placeholder="https://teams.microsoft.com/l/meetup-join/..." />
                    <button id="joinBtn" class="primary-btn">Join as Guest</button>
                </div>
                
                <div class="status-display">
                    <div id="connectionStatus" class="status-item">
                        <span class="status-dot offline"></span>
                        <span>Not connected</span>
                    </div>
                </div>
            </div>

            <div class="recording-controls" id="recordingControls" style="display: none;">
                <h2>🔴 Recording Controls</h2>
                <div class="control-buttons">
                    <button id="startRecordingBtn" class="record-btn">Start Recording</button>
                    <button id="stopRecordingBtn" class="stop-btn" style="display: none;">Stop Recording</button>
                    <button id="startTranscriptionBtn" class="transcribe-btn">Start Transcription</button>
                </div>
                
                <div class="recording-status">
                    <div id="recordingTimer" class="timer">00:00:00</div>
                    <div id="recordingIndicator" class="recording-indicator">⚪ Standby</div>
                </div>
            </div>

            <div class="transcript-panel">
                <h2>📝 Live Transcript</h2>
                <div id="transcriptContainer" class="transcript-container">
                    <div class="no-transcript">
                        Start transcription to see live captions appear here...
                    </div>
                </div>
                <div class="transcript-controls">
                    <button id="clearTranscriptBtn" class="secondary-btn">Clear</button>
                    <button id="downloadTranscriptBtn" class="secondary-btn">Download</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>AccureMD v1.0 | AI Meeting Assistant</p>
        </div>
    </div>

    <script src="/js/teams-app.js"></script>
</body>
</html>'''

# Save HTML files
with open('manifest.json', 'w') as f:
    f.write(teams_manifest)

with open('configure.html', 'w') as f:
    f.write(configure_html)

with open('index.html', 'w') as f:
    f.write(index_html)

print("✅ Created Teams App UI files")
print("📄 manifest.json - Teams app manifest with bot and tab definitions")
print("📄 configure.html - Configuration page for Teams app setup")
print("📄 index.html - Main application interface with meeting controls")