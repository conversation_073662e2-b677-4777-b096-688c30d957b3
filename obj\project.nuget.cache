{"version": 2, "dgSpecHash": "wo2YZLKAi/I=", "success": false, "projectFilePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\adaptivecards\\1.2.3\\adaptivecards.1.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.35.0\\azure.core.1.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.blobs\\12.18.0\\azure.storage.blobs.12.18.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.common\\12.17.0\\azure.storage.common.12.17.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.0\\microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.builder\\4.21.2\\microsoft.bot.builder.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.builder.integration.aspnet.core\\4.21.2\\microsoft.bot.builder.integration.aspnet.core.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.configuration\\4.21.2\\microsoft.bot.configuration.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.connector\\4.21.2\\microsoft.bot.connector.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.connector.streaming\\4.21.2\\microsoft.bot.connector.streaming.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.schema\\4.21.2\\microsoft.bot.schema.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bot.streaming\\4.21.2\\microsoft.bot.streaming.4.21.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.cognitiveservices.speech\\1.34.0\\microsoft.cognitiveservices.speech.1.34.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\8.0.0\\microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\8.0.0\\microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\8.0.0\\microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\8.0.0\\microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\8.0.0\\microsoft.extensions.hosting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\2.1.0\\microsoft.extensions.http.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\8.0.0\\microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\8.0.0\\microsoft.extensions.logging.console.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\8.0.0\\microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\8.0.0\\microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\8.0.0\\microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graph\\5.32.0\\microsoft.graph.5.32.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graph.core\\3.1.0\\microsoft.graph.core.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.55.0\\microsoft.identity.client.4.55.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.0.3\\microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.0.3\\microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.0.3\\microsoft.identitymodel.logging.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.0.3\\microsoft.identitymodel.protocols.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.0.3\\microsoft.identitymodel.protocols.openidconnect.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.0.3\\microsoft.identitymodel.tokens.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.abstractions\\1.5.0\\microsoft.kiota.abstractions.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.authentication.azure\\1.1.0\\microsoft.kiota.authentication.azure.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.http.httpclientlibrary\\1.2.0\\microsoft.kiota.http.httpclientlibrary.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.form\\1.1.0\\microsoft.kiota.serialization.form.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.json\\1.1.1\\microsoft.kiota.serialization.json.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.multipart\\1.1.0\\microsoft.kiota.serialization.multipart.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.text\\1.1.0\\microsoft.kiota.serialization.text.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.1.0\\microsoft.net.http.headers.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.rest.clientruntime\\2.3.24\\microsoft.rest.clientruntime.2.3.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.3\\netstandard.library.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\std.uritemplate\\0.0.46\\std.uritemplate.0.0.46.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.0\\system.diagnostics.eventlog.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.0.3\\system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\6.0.0\\system.io.hashing.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.0\\system.text.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": [{"code": "NU1608", "level": "Warning", "message": "Detected package version outside of dependency constraint: Microsoft.Kiota.Http.HttpClientLibrary 1.2.0 requires System.Text.Json (>= 6.0.0 && < 8.0.0) but version System.Text.Json 8.0.0 was resolved.", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "warningLevel": 1, "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}, {"code": "NU1608", "level": "Warning", "message": "Detected package version outside of dependency constraint: Microsoft.Kiota.Serialization.Json 1.1.1 requires System.Text.Json (>= 6.0.0 && < 8.0.0) but version System.Text.Json 8.0.0 was resolved.", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "warningLevel": 1, "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}, {"code": "NU1102", "level": "Error", "message": "Unable to find package Microsoft.Bot.Builder.Teams with version (>= 4.21.2)\r\n  - Found 2 version(s) in nuget.org [ Nearest version: 4.3.0-beta1 ]\r\n  - Found 0 version(s) in Microsoft Visual Studio Offline Packages", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "Microsoft.Bot.Builder.Teams", "targetGraphs": ["net8.0"]}, {"code": "NU1101", "level": "Error", "message": "Unable to find package Microsoft.Graph.Authentication. No packages exist with this id in source(s): Microsoft Visual Studio Offline Packages, nuget.org", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "Microsoft.Graph.Authentication", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-8g4q-xg66-9fp4", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "warningLevel": 1, "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-hh2w-p6rv-4g7w", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "warningLevel": 1, "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}, {"code": "NU1107", "level": "Error", "message": "Version conflict detected for System.Diagnostics.DiagnosticSource. Install/reference System.Diagnostics.DiagnosticSource 8.0.0 directly to project AccureMD.TeamsBot to resolve this issue. \r\n AccureMD.TeamsBot -> Microsoft.Extensions.Hosting 8.0.0 -> Microsoft.Extensions.Diagnostics 8.0.0 -> Microsoft.Extensions.Diagnostics.Abstractions 8.0.0 -> System.Diagnostics.DiagnosticSource (>= 8.0.0) \r\n AccureMD.TeamsBot -> Microsoft.Graph 5.32.0 -> Microsoft.Graph.Core 3.1.0 -> Microsoft.Kiota.Abstractions 1.5.0 -> System.Diagnostics.DiagnosticSource (>= 6.0.0 && < 8.0.0).", "projectPath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "filePath": "D:\\iData Project\\ASR_Bot_New\\AccureMD.TeamsBot.csproj", "libraryId": "System.Diagnostics.DiagnosticSource", "targetGraphs": ["net8.0"]}]}