# Create configuration files
appsettings_json = '''{
  "MicrosoftAppType": "MultiTenant",
  "MicrosoftAppId": "",
  "MicrosoftAppPassword": "",
  "MicrosoftAppTenantId": "",
  "BaseUrl": "https://your-app-domain.com",
  
  "ConnectionStrings": {
    "DefaultConnection": "",
    "StorageAccount": ""
  },
  
  "TranscriptionService": {
    "ApiUrl": "",
    "ApiKey": "",
    "SpeechRegion": "",
    "SpeechKey": ""
  },
  
  "Recording": {
    "StoragePath": "./recordings",
    "MaxRecordingDurationMinutes": 240,
    "SupportedFormats": ["mp4", "wav", "mp3"]
  },
  
  "Teams": {
    "AppId": "",
    "AppSecret": "",
    "TenantId": ""
  },
  
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.Bot": "Information"
    }
  },
  
  "AllowedHosts": "*"
}'''

appsettings_dev_json = '''{
  "MicrosoftAppType": "MultiTenant",
  "MicrosoftAppId": "",
  "MicrosoftAppPassword": "",
  "BaseUrl": "https://localhost:7000",
  
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Information",
      "Microsoft": "Information",
      "Microsoft.Bot": "Debug"
    }
  },
  
  "Teams": {
    "AppId": "",
    "AppSecret": "",
    "TenantId": ""
  }
}'''

# Save configuration files
with open('appsettings.json', 'w') as f:
    f.write(appsettings_json)

with open('appsettings.Development.json', 'w') as f:
    f.write(appsettings_dev_json)

print("✅ Created configuration files")
print("📄 appsettings.json - Production configuration with empty placeholders")
print("📄 appsettings.Development.json - Development configuration")